﻿﻿﻿﻿﻿﻿﻿﻿﻿﻿﻿﻿﻿﻿﻿﻿﻿﻿<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON><PERSON> (<PERSON>) - <PERSON><PERSON><PERSON><PERSON></title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="../styles.css">
    <style>
        /* Navigation styles */
        @media (max-width: 767px) {
            .nav-links.show {
                display: flex !important;
                flex-direction: column !important;
                position: absolute !important;
                top: 60px !important;
                left: 0 !important;
                width: 100% !important;
                background-color: #fff !important;
                z-index: 1000 !important;
                box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1) !important;
                margin: 0 !important;
                padding: 0 !important;
                border-top: 1px solid #ddd !important;
                visibility: visible !important;
                opacity: 1 !important;
            }
            
            .dark-mode .nav-links.show {
                background-color: #252525 !important;
            }
            
            .dropdown-content {
                display: none !important;
            }
            
            .dropdown.active .dropdown-content {
                display: block !important;
                position: static !important;
                width: 100% !important;
                background-color: rgba(0, 0, 0, 0.03) !important;
                box-shadow: none !important;
                border-top: 1px solid #eee !important;
            }
            
            .dark-mode .dropdown.active .dropdown-content {
                background-color: rgba(255, 255, 255, 0.05) !important;
                border-top: 1px solid #333 !important;
            }
        }
        
        /* Chess game styles */
        .game-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .game-layout {
            display: grid;
            grid-template-columns: 250px 1fr 250px;
            gap: 20px;
            align-items: start;
        }
        
        .game-options {
            display: flex !important;
            flex-direction: column !important;
            gap: 20px;
            background-color: #f5f5f5;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .options-section {
            display: flex !important;
            flex-direction: row !important;
            gap: 10px;
            padding-bottom: 15px;
            border-bottom: 1px solid rgba(0,0,0,0.1);
        }
        
        .game-status-section {
            display: flex !important;
            flex-direction: column !important;
            gap: 10px;
            padding-bottom: 15px;
            border-bottom: 1px solid rgba(0,0,0,0.1);
        }
        
        .difficulty-options {
            display: flex !important;
            flex-direction: row !important;
            gap: 10px;
            padding-bottom: 15px;
            border-bottom: 1px solid rgba(0,0,0,0.1);
        }
        
        .option-row {
            display: flex;
            align-items: center;
            margin: 5px 0;
            position: relative;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            transition: all 0.2s ease;
        }
        
        .option-row label {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            cursor: pointer;
            width: 100%;
            height: 100%;
        }
        
        .option-icon {
            font-size: 1.8em;
            width: 30px;
            text-align: center;
        }
        
        /* Active state for option-row */
        .option-row:has(input:checked) {
            background-color: #4CAF50;
            box-shadow: 0 0 5px rgba(76, 175, 80, 0.5);
        }
        
        /* Fallback for browsers that don't support :has */
        .option-row.active {
            background-color: #4CAF50;
            box-shadow: 0 0 5px rgba(76, 175, 80, 0.5);
        }
        
        .chess-symbol {
            font-size: 1.8em;
            display: inline-block;
            font-weight: normal;
            font-family: 'Arial Unicode MS', 'Segoe UI Symbol', sans-serif;
        }
        
        .chess-game {
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        
        .game-info {
            display: flex;
            flex-direction: column;
            gap: 15px;
            background-color: #f5f5f5;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        /* Chess clock styles */
        .chess-clocks {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }
        
        .clock-options {
            display: flex;
            flex-direction: column;
            gap: 10px;
            padding-bottom: 15px;
            border-bottom: 1px solid rgba(0,0,0,0.1);
        }
        
        .time-settings {
            display: flex;
            flex-direction: column;
            gap: 10px;
            margin-top: 10px;
            padding: 10px;
            background-color: rgba(0,0,0,0.03);
            border-radius: 5px;
        }
        
        .time-settings input {
            width: 80px;
            padding: 5px;
            border: 1px solid #ccc;
            border-radius: 4px;
        }
        
        .player-clocks {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }
        
        .clock-container {
            display: flex;
            flex-direction: row;
            align-items: center;
            gap: 10px;
            padding: 10px;
            border-radius: 8px;
            background-color: rgba(0,0,0,0.05);
            border: 1px solid rgba(0,0,0,0.1);
            transition: all 0.2s ease;
        }
        
        .clock-header {
            display: flex;
            justify-content: flex-start;
            align-items: center;
            min-width: 80px;
        }
        
        .clock-display {
            font-size: 2rem;
            font-weight: bold;
            text-align: center;
            font-family: 'Courier New', monospace;
            padding: 5px 10px;
            background-color: rgba(0,0,0,0.1);
            border-radius: 5px;
            flex-grow: 1;
        }
        
        .black-clock .clock-display {
            color: #000;
        }
        
        .white-clock .clock-display {
            color: #000;
        }
        
        .active-clock {
            border: 2px solid #4CAF50;
            background-color: rgba(76, 175, 80, 0.1); /* Light green background */
        }
        
        .player-status {
            display: flex;
            flex-direction: column;
            gap: 15px;
            margin-top: 10px;
        }
        
        .player-row {
            display: flex;
            flex-direction: column;
            gap: 5px;
            padding: 10px;
            border-radius: 6px;
            background-color: rgba(0,0,0,0.03);
            border: 1px solid rgba(0,0,0,0.1);
        }
        
        .player-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-weight: bold;
        }
        
        .player-label {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .player-icon {
            font-size: 1.8em;
            font-weight: normal;
        }
        
        .white-icon {
            color: #000;
        }
        
        .black-icon {
            color: #000;
        }
        
        .player-score {
            font-size: 1.2em;
            color: #4CAF50;
        }
        
        .player-captured {
            display: flex;
            flex-direction: row;
            flex-wrap: wrap;
            gap: 8px;
            min-height: 40px;
            align-items: center;
        }
        
        .captured-piece {
            font-size: 26px;
            display: inline-block;
            margin: 2px;
            width: 32px;
            height: 32px;
            text-align: center;
            line-height: 32px;
            background-color: rgba(255, 255, 255, 0.3);
            border-radius: 4px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
        }
        
        .white-captured, .black-captured {
            display: flex;
            flex-direction: row;
            flex-wrap: wrap;
            gap: 8px;
            margin-top: 5px;
            min-height: 40px;
            padding: 8px;
            border-radius: 6px;
            background-color: rgba(0,0,0,0.05);
            align-items: center;
            border: 1px solid rgba(0,0,0,0.1);
        }
        
        .notification {
            min-height: 20px;
            text-align: center;
            font-weight: bold;
            color: #d32f2f;
            margin: 10px 0;
        }
        
        .chess-board {
            display: grid;
            grid-template-columns: repeat(8, 65px);
            grid-template-rows: repeat(8, 65px);
            border: 2px solid #333;
            margin-bottom: 20px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }
        
        .chess-square {
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 36px; /* Increased from 30px to 36px */
            cursor: pointer;
            position: relative;
            text-align: center;
            line-height: 1;
            box-sizing: border-box;
        }
        
        /* Ensure chess pieces are centered */
        .chess-square span {
            display: flex;
            justify-content: center;
            align-items: center;
            width: 100%;
            height: 100%;
        }
        
        .rank-label, .file-label {
            position: absolute;
            font-size: 14px; /* Larger font size for better visibility */
            color: #000; /* Black color for maximum contrast */
            font-weight: bold; /* Make labels bold */
            pointer-events: none;
            text-shadow: 0 0 3px rgba(255, 255, 255, 1); /* Stronger white shadow for better visibility */
            z-index: 10; /* Ensure labels appear above other elements */
        }
        
        .rank-label {
            left: 5px;
            top: 5px;
            transform: none; /* Ensure no centering transformation is applied */
        }
        
        .file-label {
            right: 5px;
            bottom: 5px;
            transform: none; /* Ensure no centering transformation is applied */
        }
        
        /* Chess piece positioning */
        .chess-piece {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: 5; /* Lower than labels */
            width: auto;
            height: auto;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        
        /* Ensure labels are visible in both light and dark modes */
        body.dark-mode .rank-label, 
        body.dark-mode .file-label {
            color: #000; /* Keep black color in dark mode */
            text-shadow: 0 0 3px rgba(255, 255, 255, 1); /* Same shadow in dark mode */
        }
        
        .chess-square.white {
            background-color: #f0d9b5;
        }
        
        .chess-square.black {
            background-color: #b58863;
        }
        
        .chess-square.selected {
            background-color: #aad2ff;
        }
        
        .chess-square.valid-move {
            position: relative;
        }
        
        .chess-square.valid-move::after {
            content: "";
            position: absolute;
            width: 20px;
            height: 20px;
            background-color: rgba(0, 128, 0, 0.5);
            border-radius: 50%;
        }
        
        .chess-square.check {
            background-color: #ff6b6b;
        }
        
        .game-controls {
            display: flex !important;
            flex-direction: row !important;
            gap: 5px !important;
            justify-content: space-between !important;
            flex-wrap: wrap !important;
        }
        
        .game-controls button {
            padding: 10px;
            background-color: var(--primary-color);
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-weight: bold;
            width: 40px !important;
            height: 40px !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            z-index: 10;
            font-size: 1.2rem;
            transition: all 0.2s ease;
        }
        
        .game-controls button i {
            font-size: 1.2em;
        }
        
        .game-controls button:hover {
            background-color: #0056b3;
            transform: translateY(-2px);
        }
        
        /* Promotion modal */
        .promotion-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.7);
            z-index: 1000;
            justify-content: center;
            align-items: center;
        }
        
        .promotion-options {
            display: flex;
            background-color: white;
            padding: 20px;
            border-radius: 5px;
        }
        
        .promotion-piece {
            font-size: 40px;
            margin: 0 10px;
            cursor: pointer;
            display: flex;
            justify-content: center;
            align-items: center;
            width: 60px;
            height: 60px;
            border-radius: 5px;
            transition: background-color 0.2s;
        }
        
        .promotion-piece:hover {
            background-color: rgba(0, 0, 0, 0.1);
        }
        
        /* Ensure promotion pieces maintain their colors in dark mode */
        body.dark-mode .promotion-options {
            background-color: #333;
        }
        
        body.dark-mode .promotion-piece:hover {
            background-color: rgba(255, 255, 255, 0.2);
        }
        
        /* Fullscreen mode */
        .fullscreen-container {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.9);
            z-index: 1000;
            justify-content: center;
            align-items: center;
            padding: 20px;
            box-sizing: border-box;
        }

        .fullscreen-container.active {
            display: flex;
        }

        .fullscreen-layout {
            display: flex;
            width: 100%;
            height: 100%;
            justify-content: center;
            align-items: center;
            gap: 20px;
        }
        
        .fullscreen-board-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 15px;
        }

        .fullscreen-chess-board {
            display: grid;
            grid-template-columns: repeat(8, 90px);
            grid-template-rows: repeat(8, 90px);
            border: 3px solid #333;
        }
        
        .fullscreen-chess-board .chess-square {
            font-size: 50px; /* Larger font size for pieces in fullscreen mode */
        }
        
        .fullscreen-chess-board .rank-label,
        .fullscreen-chess-board .file-label {
            font-size: 16px; /* Larger labels in fullscreen mode */
        }
        
        .fullscreen-sidebar {
            width: 250px;
            display: flex;
            flex-direction: column;
            align-items: center;
            background-color: rgba(0, 0, 0, 0.3);
            border-radius: 8px;
            padding: 15px;
            gap: 15px;
            height: auto;
            position: relative;
            z-index: 20;
            margin-left: 20px;
        }

        .fullscreen-controls {
            display: flex;
            gap: 10px;
            width: 100%;
            justify-content: center;
            align-items: center;
            margin-top: 10px;
            margin-bottom: 10px;
        }

        .game-button.control-button {
            background-color: var(--primary-color);
            color: white;
            border: none;
            border-radius: 4px;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            z-index: 10;
            font-size: 1.2rem;
            transition: all 0.2s ease;
        }

        .game-button.control-button:hover {
            background-color: #0056b3;
            transform: translateY(-2px);
        }

        .reset-button {
            background-color: #3498db !important;
        }

        .reset-scores-button {
            background-color: #e67e22 !important;
        }

        .undo-button {
            background-color: #9b59b6 !important;
        }

        .exit-button {
            background-color: #e74c3c !important;
        }

        .fullscreen-game-options {
            width: 100%;
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .fullscreen-mode-options, 
        .fullscreen-difficulty-options {
            display: flex !important;
            justify-content: center;
            gap: 10px;
            width: 100%;
        }

        .fullscreen-difficulty-options {
            margin-top: 5px;
        }

        .fullscreen-sidebar h4 {
            color: white;
            margin: 0;
            font-size: 0.9rem;
            text-align: center;
            width: 100%;
        }

        .control-option {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 40px;
            height: 40px;
            background-color: rgba(255, 255, 255, 0.1);
            border-radius: 50%; /* Changed to circular */
            cursor: pointer;
            transition: all 0.2s ease;
            position: relative;
        }

        .control-option:hover {
            background-color: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
        }

        .control-option input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }

        .control-option i {
            font-size: 1.2rem;
            color: white;
        }

        /* Style for selected options */
        .control-option input:checked + i,
        .control-option input:checked + span {
            color: white;
        }
        
        /* Green background for selected options */
        .control-option input:checked {
            background-color: transparent;
        }
        
        .control-option input:checked ~ .control-option {
            background-color: #4CAF50;
        }
        
        /* Apply green background to the parent when radio is checked */
        .control-option input:checked + i,
        .control-option input:checked + span {
            color: white;
        }
        
        .control-option input:checked {
            background-color: transparent;
        }
        
        /* Make the selected option have green background */
        .control-option:has(input:checked) {
            background-color: #4CAF50;
        }
        
        /* Fallback for browsers that don't support :has */
        .control-option.selected {
            background-color: #4CAF50;
        }

        .fullscreen-player-info {
            display: flex;
            justify-content: space-around;
            width: 100%;
            margin-top: 15px;
            background-color: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            padding: 12px;
        }

        .fullscreen-player-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            color: white;
            padding: 8px 15px;
            border-radius: 6px;
        }

        .fullscreen-player-icon {
            font-size: 0.9rem;
            margin-bottom: 5px;
        }

        .player-white .fullscreen-player-icon .chess-symbol {
            color: #f0f0f0;
        }

        .player-black .fullscreen-player-icon .chess-symbol {
            color: #aaaaaa;
        }

        .fullscreen-player-score {
            font-size: 1.2rem;
            font-weight: bold;
            margin-top: 5px;
        }

        .score-white {
            color: #f0f0f0;
        }

        .score-black {
            color: #aaaaaa;
        }

        .fullscreen-captured-pieces {
            display: flex;
            flex-direction: column;
            gap: 10px;
            width: 100%;
            margin-top: 10px;
        }
        
        .captured-section {
            display: flex;
            flex-direction: column;
            gap: 5px;
            width: 100%;
            margin-bottom: 10px;
        }
        
        .captured-label {
            color: white;
            font-size: 0.85rem;
            opacity: 0.8;
            margin-left: 2px;
        }

        .fullscreen-captured {
            min-height: 30px;
            background-color: rgba(255, 255, 255, 0.1);
            border-radius: 4px;
            padding: 8px;
            display: flex;
            flex-wrap: wrap;
            gap: 5px;
            align-items: center;
            position: relative;
            z-index: 10;
        }
        
        .fullscreen-captured .captured-piece {
            font-size: 22px;
            display: inline-block;
            width: 28px;
            height: 28px;
            text-align: center;
            line-height: 28px;
            background-color: rgba(255, 255, 255, 0.2);
            border-radius: 4px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
            position: relative;
        }
        
        .white-captured .captured-piece {
            color: #000;
        }
        
        .black-captured .captured-piece {
            color: #fff;
        }
        
        /* Dark mode styles - now keeping the same chess board colors as light mode */
        body.dark-mode .chess-square.white {
            background-color: #f0d9b5; /* Same as light mode */
        }
        
        body.dark-mode .chess-square.black {
            background-color: #b58863; /* Same as light mode */
        }
        
        body.dark-mode .chess-square.selected {
            background-color: #aad2ff; /* Same as light mode */
        }
        
        /* Ensure chess piece colors remain consistent in dark mode */
        body.dark-mode .chess-square span {
            color: inherit; /* Inherit color from the piece's original color */
        }
        
        /* White pieces - now with transparent background and black color */
        .chess-piece.white {
            color: #000;
            background-color: transparent;
            text-shadow: 0 0 1px #fff, 0 0 2px #fff; /* Add shadow for better visibility */
        }
        
        /* Black pieces */
        .chess-piece.black {
            color: #000;
            text-shadow: 0 0 1px #fff, 0 0 2px #fff; /* Add shadow for better visibility */
        }
        
        /* Ensure these colors are preserved in dark mode */
        body.dark-mode .chess-piece.white {
            color: #000;
            background-color: transparent;
            text-shadow: 0 0 1px #fff, 0 0 2px #fff;
        }
        
        body.dark-mode .chess-piece.black {
            color: #000;
            text-shadow: 0 0 1px #fff, 0 0 2px #fff;
        }
        
        /* Dark mode styles for player rows */
        body.dark-mode .player-row {
            background-color: rgba(255,255,255,0.1);
            border-color: rgba(255,255,255,0.2);
        }
        
        body.dark-mode .player-score {
            color: #6dca70;
        }
        
        body.dark-mode .promotion-options {
            background-color: #333;
            color: white;
        }
        
        /* Improved text readability in dark mode */
        body.dark-mode {
            color: #e0e0e0; /* Lighter text color for better readability */
        }
        
        body.dark-mode .game-options,
        body.dark-mode .game-info {
            background-color: #2a2a2a;
            color: #e0e0e0;
        }
        
        body.dark-mode button {
            color: #fff;
        }
        
        body.dark-mode .notification {
            color: #ff8080; /* Brighter red for notifications in dark mode */
        }
        
        /* Responsive design */
        @media (max-width: 1100px) {
            .game-layout {
                grid-template-columns: 1fr;
                grid-template-rows: auto auto auto;
            }
            
            .game-options {
                flex-direction: row;
                flex-wrap: wrap;
                justify-content: center;
                gap: 30px;
                margin-bottom: 20px;
            }
            
            .game-info {
                flex-direction: row;
                justify-content: space-between;
                flex-wrap: wrap;
                margin-top: 20px;
            }
            
            .game-mode, .game-controls {
                flex: 1;
                min-width: 200px;
                display: flex !important;
                flex-direction: row !important;
                gap: 5px !important;
            }
            
            .difficulty-options {
                flex: 1;
                min-width: 200px;
                display: flex !important;
                flex-direction: row !important;
                gap: 5px !important;
            }
            
            .player-status {
                flex: 1;
                min-width: 200px;
            }
        }
        
        @media (max-width: 600px) {
            .chess-board {
                grid-template-columns: repeat(8, 40px);
                grid-template-rows: repeat(8, 40px);
            }
            
            .chess-square {
                font-size: 30px; /* Increased from 24px to 30px */
            }
            
            .rank-label, .file-label {
                font-size: 12px; /* Larger for better visibility on mobile */
                font-weight: bold; /* Keep bold on mobile */
            }
            
            .game-controls {
                flex-wrap: wrap !important;
                flex-direction: row !important;
                display: flex !important;
            }
            
            .fullscreen-layout {
                flex-direction: column;
                padding-top: 20px;
            }
            
            .fullscreen-board-container {
                width: 100%;
                margin-bottom: 15px;
            }
            
            .fullscreen-chess-board {
                grid-template-columns: repeat(8, 45px);
                grid-template-rows: repeat(8, 45px);
                margin-bottom: 15px;
            }
            
            .fullscreen-chess-board .chess-square {
                font-size: 32px;
            }
            
            .fullscreen-sidebar {
                width: 100%;
                max-width: 400px;
                padding: 10px;
                margin-left: 0;
            }
            
            .fullscreen-controls {
                flex-wrap: wrap;
            }
            
            .fullscreen-game-options {
                flex-direction: row;
                flex-wrap: wrap;
                justify-content: center;
            }
            
            .fullscreen-mode-options, 
            .fullscreen-difficulty-options {
                justify-content: center;
            }
            
            .fullscreen-player-info {
                margin-top: 10px;
            }
            
            .game-options {
                flex-direction: column;
                align-items: center;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation and header would go here -->
    <nav>
        <div class="container nav-container">
            <div class="logo">
                <a href="../index.html">Opiskelen Suomea</a>
            </div>
            <div class="theme-toggle-container mobile-only-theme-toggle">
                <button class="theme-toggle-button" id="game-toggle-dark-mobile"><i class="fas fa-moon"></i></button>
            </div>
            <button class="mobile-menu-toggle" id="mobile-menu-toggle">
                <i class="fas fa-bars menu-icon-bars"></i>
                <i class="fas fa-times menu-icon-times" style="display: none;"></i>
            </button>
            <ul class="nav-links" id="nav-links">
                <li><a href="../index.html" class="nav-link">Home</a></li>
                <li><a href="../audio.html" class="nav-link">Audio</a></li>
                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn nav-link">Videos</a>
                    <div class="dropdown-content" id="channels-dropdown">
                        <a href="../video.html?channel=kuulostaahyvalta" data-channel-key="kuulostaahyvalta">Kuulostaa Hyvältä</a>
                        <a href="../video.html?channel=finnishcrashcourse" data-channel-key="finnishcrashcourse">Finnish Crash Course</a>
                        <a href="../video.html?channel=finnishtogo" data-channel-key="finnishtogo">Finnish To Go</a>
                        <a href="../video.html?channel=suomenkurssiyt" data-channel-key="suomenkurssiyt">Suomen Kurssi</a>
                        <a href="../video.html?channel=pipsapossu" data-channel-key="pipsapossu">Pipsa Possu</a>
                        <a href="../video.html?channel=katchatsfinnish" data-channel-key="katchatsfinnish">KatChats Finnish</a>
                        <div class="dropdown-submenu">
                            <a href="javascript:void(0)" class="submenu-header">Yle Areena</a>
                            <div class="dropdown-content">
                                <a href="../video.html?channel=yleareena" data-channel-key="yleareena">Yle Areena 1</a>
                                <a href="../video.html?channel=yleareena2" data-channel-key="yleareena2">Yle Areena 2</a>
                                <a href="../video.html?channel=yleareena3" data-channel-key="yleareena3">Yle Areena 3</a>
                                <a href="../video.html?channel=yleareena4" data-channel-key="yleareena4">Yle Areena 4</a>
                                <a href="../video.html?channel=yleareena5" data-channel-key="yleareena5">Yle Areena 5</a>
                            </div>
                        </div>
                        <div class="dropdown-submenu">
                            <a href="javascript:void(0)" class="submenu-header">Kaapo - WildBrain</a>
                            <div class="dropdown-content">
                                <a href="../video.html?channel=kaapowildbrain" data-channel-key="kaapowildbrain">Kaapo - WildBrain 1</a>
                                <a href="../video.html?channel=kaapowildbrain2" data-channel-key="kaapowildbrain2">Kaapo - WildBrain 2</a>
                                <a href="../video.html?channel=kaapowildbrain3" data-channel-key="kaapowildbrain3">Kaapo - WildBrain 3</a>
                                <a href="../video.html?channel=kaapowildbrain4" data-channel-key="kaapowildbrain4">Kaapo - WildBrain 4</a>
                            </div>
                        </div>
                        <div class="dropdown-submenu">
                            <a href="javascript:void(0)" class="submenu-header">Yle Pikku Kakkonen</a>
                            <div class="dropdown-content">
                                <a href="../video.html?channel=ylepikkukakkonen" data-channel-key="ylepikkukakkonen">Yle Pikku Kakkonen 1</a>
                                <a href="../video.html?channel=ylepikkukakkonen2" data-channel-key="ylepikkukakkonen2">Yle Pikku Kakkonen 2</a>
                                <a href="../video.html?channel=ylepikkukakkonen3" data-channel-key="ylepikkukakkonen3">Yle Pikku Kakkonen 3</a>
                                <a href="../video.html?channel=ylepikkukakkonen4" data-channel-key="ylepikkukakkonen4">Yle Pikku Kakkonen 4</a>
                                <a href="../video.html?channel=ylepikkukakkonen5" data-channel-key="ylepikkukakkonen5">Yle Pikku Kakkonen 5</a>
                                <a href="../video.html?channel=ylepikkukakkonen6" data-channel-key="ylepikkukakkonen6">Yle Pikku Kakkonen 6</a>
                            </div>
                        </div>
                    </div>
                </li>
                <li><a href="../finnish_grammar/index.html">Grammar</a></li>
                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Categories</a>
                    <div class="dropdown-content">
                        <a href="../index.html#daily-life">Daily Life</a>
                        <a href="../index.html#web-development">Web Development</a>
                        <a href="../index.html#cleaner">Cleaner</a>
                        <a href="../index.html#kitchen-assistant">Kitchen Assistant</a>
                        <a href="../index.html#warehouse">Warehouse</a>
                    </div>
                </li>
                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Entertainment</a>
                    <div class="dropdown-content">
                        <a href="../games.html">Games</a>
                    </div>
                </li>
                <li class="theme-toggle-container">
                    <button class="theme-toggle-button" id="game-toggle-dark"><i class="fas fa-moon"></i></button>
                </li>
            </ul>
        </div>
    </nav>

    <main class="content">
        <div class="game-container">
            <h1>Shakki (Chess)</h1>
            
            <div class="game-layout">
                <!-- Left column: Game options and status -->
                <div class="game-options">
                    <div class="game-controls">
                        <button id="reset-button" class="reset-button" title="Reset Game"><i class="fas fa-redo"></i></button>
                        <button id="reset-scores-button" class="reset-scores-button" title="Reset Scores"><i class="fas fa-eraser"></i></button>
                        <button id="undo-button" class="undo-button" title="Undo Move"><i class="fas fa-undo"></i></button>
                        <button id="fullscreen-button" class="fullscreen-button" title="Fullscreen"><i class="fas fa-expand"></i></button>
                    </div>
                    
                    <div class="options-section">
                        <div class="option-row">
                            <input type="radio" id="computer-mode" name="game-mode" value="computer" checked>
                            <label for="computer-mode" title="Play against computer">
                                <span class="chess-symbol option-icon">🤖</span>
                            </label>
                        </div>
                        <div class="option-row">
                            <input type="radio" id="two-player-mode" name="game-mode" value="two-player">
                            <label for="two-player-mode" title="Play with friend">
                                <span class="chess-symbol option-icon">♚</span>
                            </label>
                        </div>
                    </div>
                    
                    <div class="difficulty-options" id="difficulty-options">
                        <div class="option-row">
                            <input type="radio" id="easy" name="difficulty" value="easy" checked>
                            <label for="easy" title="Easy level">
                                <span class="chess-symbol option-icon">♟</span>
                            </label>
                        </div>
                        <div class="option-row">
                            <input type="radio" id="normal" name="difficulty" value="medium">
                            <label for="normal" title="Normal level">
                                <span class="chess-symbol option-icon">♞</span>
                            </label>
                        </div>
                        <div class="option-row">
                            <input type="radio" id="hard" name="difficulty" value="hard">
                            <label for="hard" title="Hard level">
                                <span class="chess-symbol option-icon">♛</span>
                            </label>
                        </div>
                    </div>
                    
                    <!-- Game Status section -->
                    <div class="game-status-section">
                        <div class="player-status">
                            <div class="player-row white-player">
                                <div class="player-info">
                                    <div class="player-label">
                                        <span class="chess-symbol player-icon white-icon">♔</span>
                                    </div>
                                    <span class="player-score" id="score-white">0</span>
                                </div>
                                <div class="player-captured" id="white-captured"></div>
                            </div>
                            <div class="player-row black-player">
                                <div class="player-info">
                                    <div class="player-label">
                                        <span class="chess-symbol player-icon black-icon">🤖</span>
                                    </div>
                                    <span class="player-score" id="score-black">0</span>
                                </div>
                                <div class="player-captured" id="black-captured"></div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Middle column: Chess board -->
                <div class="chess-game">
                    <div id="notification" class="notification"></div>
                    <div id="chess-board" class="chess-board"></div>
                </div>
                
                <!-- Right column: Chess clocks -->
                <div class="game-info chess-clocks">
                    <div class="player-clocks">
                        <div class="clock-container black-clock">
                            <div class="clock-header">
                                <div class="player-label">
                                    <span class="chess-symbol player-icon black-icon">🤖</span>
                                </div>
                            </div>
                            <div class="clock-display" id="black-clock">05:00</div>
                        </div>
                        
                        <div class="clock-container white-clock">
                            <div class="clock-header">
                                <div class="player-label">
                                    <span class="chess-symbol player-icon white-icon">♔</span>
                                </div>
                            </div>
                            <div class="clock-display" id="white-clock">05:00</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <footer>
        <!-- Footer content -->
    </footer>

    <div class="promotion-modal" id="promotion-modal">
        <div class="promotion-options" id="promotion-options">
            <!-- Promotion pieces will be added by JavaScript -->
        </div>
    </div>
    
    <div id="fullscreen-container" class="fullscreen-container">
        <div class="fullscreen-layout">
            <div class="fullscreen-board-container">
                <div id="fullscreen-chess-board" class="fullscreen-chess-board"></div>
                
                <div class="fullscreen-controls">
                    <button class="game-button control-button reset-button" id="fullscreen-reset-button">
                        <i class="fas fa-redo"></i>
                    </button>
                    <button class="game-button control-button reset-scores-button" id="fullscreen-reset-scores-button">
                        <i class="fas fa-eraser"></i>
                    </button>
                    <button class="game-button control-button undo-button" id="fullscreen-undo-button">
                        <i class="fas fa-undo"></i>
                    </button>
                    <button class="game-button control-button exit-button" id="exit-fullscreen-button">
                        <i class="fas fa-compress"></i>
                    </button>
                </div>
            </div>
            
            <div class="fullscreen-sidebar">
                <!-- Chess clocks for fullscreen mode -->
                <div class="player-clocks">
                    <div class="clock-container black-clock">
                        <div class="clock-header">
                            <div class="player-label">
                                <span class="chess-symbol player-icon black-icon">🤖</span>
                            </div>
                        </div>
                        <div class="clock-display" id="fullscreen-black-clock">05:00</div>
                    </div>
                    
                    <div class="clock-container white-clock">
                        <div class="clock-header">
                            <div class="player-label">
                                <span class="chess-symbol player-icon white-icon">♔</span>
                            </div>
                        </div>
                        <div class="clock-display" id="fullscreen-white-clock">05:00</div>
                    </div>
                </div>
                
                <div class="fullscreen-game-options">
                    <div>
                        <h4>Game Mode</h4>
                        <div class="fullscreen-mode-options">
                            <label class="control-option mode-option" title="Play against computer">
                                <input type="radio" name="fullscreen-game-mode" value="computer" checked>
                                <span class="chess-symbol">🤖</span>
                            </label>
                            <label class="control-option mode-option" title="Play with friend">
                                <input type="radio" name="fullscreen-game-mode" value="friend">
                                <span class="chess-symbol">♚</span>
                            </label>
                        </div>
                    </div>
                    
                    <div id="fullscreen-difficulty-options">
                        <h4>Difficulty</h4>
                        <div class="fullscreen-difficulty-options">
                            <label class="control-option diff-option" title="Easy level">
                                <input type="radio" name="fullscreen-difficulty" value="easy" checked>
                                <span class="chess-symbol">♟</span>
                            </label>
                            <label class="control-option diff-option" title="Normal level">
                                <input type="radio" name="fullscreen-difficulty" value="normal">
                                <span class="chess-symbol">♞</span>
                            </label>
                            <label class="control-option diff-option" title="Hard level">
                                <input type="radio" name="fullscreen-difficulty" value="hard">
                                <span class="chess-symbol">♛</span>
                            </label>
                        </div>
                    </div>
                    
                    <div class="fullscreen-player-info">
                        <div class="fullscreen-player-item player-white">
                            <div class="fullscreen-player-icon"><span class="chess-symbol">♔</span></div>
                            <div class="fullscreen-player-score score-white"><span id="fullscreen-score-white">0</span></div>
                        </div>
                        <div class="fullscreen-player-item player-black">
                            <div class="fullscreen-player-icon"><span class="chess-symbol">🤖</span></div>
                            <div class="fullscreen-player-score score-black"><span id="fullscreen-score-black">0</span></div>
                        </div>
                    </div>
                    
                    <div class="fullscreen-captured-pieces">
                        <div class="captured-section">
                            <div class="captured-label">Captured Black Pieces:</div>
                            <div class="fullscreen-captured white-captured" id="fullscreen-white-captured"></div>
                        </div>
                        <div class="captured-section">
                            <div class="captured-label">Captured White Pieces:</div>
                            <div class="fullscreen-captured black-captured" id="fullscreen-black-captured"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="../script.js"></script>
    <script>
        // Global variables for navigation elements
        let mobileMenuToggle;
        let navLinks;

        // Initialize dark mode and mobile menu
        document.addEventListener("DOMContentLoaded", function() {
            // Giữ nguyên code xử lý menu và dark mode của bạn
            mobileMenuToggle = document.getElementById("mobile-menu-toggle");
            navLinks = document.getElementById("nav-links");

            if (mobileMenuToggle && navLinks) {
                mobileMenuToggle.addEventListener("click", function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    navLinks.classList.toggle("show");
                    this.classList.toggle("active");

                    if (!navLinks.classList.contains("show")) {
                        const activeDropdowns = document.querySelectorAll('.dropdown.active');
                        activeDropdowns.forEach(dropdown => dropdown.classList.remove('active'));
                        const activeSubmenus = document.querySelectorAll('.dropdown-submenu.active');
                        activeSubmenus.forEach(submenu => submenu.classList.remove('active'));
                    }

                    const barsIcon = mobileMenuToggle.querySelector('.menu-icon-bars');
                    const timesIcon = mobileMenuToggle.querySelector('.menu-icon-times');
                    if (barsIcon && timesIcon) {
                        if (navLinks.classList.contains('show')) {
                            barsIcon.style.display = 'none';
                            timesIcon.style.display = 'inline-block';
                        } else {
                            barsIcon.style.display = 'inline-block';
                            timesIcon.style.display = 'none';
                        }
                    }
                });

                window.addEventListener('resize', function() {
                    if (window.innerWidth > 767) {
                        navLinks.style.display = "flex";
                        navLinks.style.visibility = "visible";
                        navLinks.style.opacity = "1";
                        navLinks.style.position = "static";
                        navLinks.style.flexDirection = "row";
                    } else if (!navLinks.classList.contains("show")) {
                        navLinks.style.display = "none";
                        navLinks.style.visibility = "hidden";
                        navLinks.style.opacity = "0";
                    }
                });

                if (window.innerWidth <= 767) {
                    navLinks.classList.remove("show");
                    navLinks.style.display = "none";
                    navLinks.style.visibility = "hidden";
                    navLinks.style.opacity = "0";
                } else {
                    navLinks.style.display = "flex";
                    navLinks.style.visibility = "visible";
                    navLinks.style.opacity = "1";
                }
            }

            const darkMode = localStorage.getItem("darkMode");
            if (darkMode === "enabled") {
                document.body.classList.add("dark-mode");
                const darkModeBtn = document.getElementById("game-toggle-dark");
                if (darkModeBtn) {
                    const icon = darkModeBtn.querySelector("i");
                    if (icon) {
                        icon.classList.remove("fa-moon");
                        icon.classList.add("fa-sun");
                    }
                }
                const mobileDarkModeBtn = document.getElementById("game-toggle-dark-mobile");
                if (mobileDarkModeBtn) {
                    const mobileIcon = mobileDarkModeBtn.querySelector("i");
                    if (mobileIcon) {
                        mobileIcon.classList.remove("fa-moon");
                        mobileIcon.classList.add("fa-sun");
                    }
                }
            }

            const darkModeToggle = document.getElementById("game-toggle-dark");
            if (darkModeToggle) darkModeToggle.addEventListener("click", toggleDarkMode);
            const mobileDarkModeToggle = document.getElementById("game-toggle-dark-mobile");
            if (mobileDarkModeToggle) mobileDarkModeToggle.addEventListener("click", toggleDarkMode);

            const dropdowns = document.querySelectorAll(".dropdown");
            dropdowns.forEach((dropdown) => {
                const dropbtn = dropdown.querySelector(".dropbtn");
                if (dropbtn) {
                    dropbtn.addEventListener("click", function (e) {
                        if (window.innerWidth <= 767) {
                            e.preventDefault();
                            e.stopPropagation();
                            if (navLinks) navLinks.classList.add('show');
                            if (mobileMenuToggle) {
                                mobileMenuToggle.classList.add('active');
                                const barsIcon = mobileMenuToggle.querySelector('.menu-icon-bars');
                                const timesIcon = mobileMenuToggle.querySelector('.menu-icon-times');
                                if (barsIcon && timesIcon) {
                                    barsIcon.style.display = 'none';
                                    timesIcon.style.display = 'inline-block';
                                }
                            }
                            dropdowns.forEach((otherDropdown) => {
                                if (otherDropdown !== dropdown && otherDropdown.classList.contains("active")) {
                                    otherDropdown.classList.remove("active");
                                }
                            });
                            dropdown.classList.toggle("active");
                        }
                    });
                }
            });
        });

        function toggleDarkMode() {
            document.body.classList.toggle("dark-mode");
            if (document.body.classList.contains("dark-mode")) {
                localStorage.setItem("darkMode", "enabled");
                const darkModeBtn = document.getElementById("game-toggle-dark");
                if (darkModeBtn) {
                    const icon = darkModeBtn.querySelector("i");
                    if (icon) {
                        icon.classList.remove("fa-moon");
                        icon.classList.add("fa-sun");
                    }
                }
                const mobileDarkModeBtn = document.getElementById("game-toggle-dark-mobile");
                if (mobileDarkModeBtn) {
                    const mobileIcon = mobileDarkModeBtn.querySelector("i");
                    if (mobileIcon) {
                        mobileIcon.classList.remove("fa-moon");
                        mobileIcon.classList.add("fa-sun");
                    }
                }
            } else {
                localStorage.setItem("darkMode", "disabled");
                const darkModeBtn = document.getElementById("game-toggle-dark");
                if (darkModeBtn) {
                    const icon = darkModeBtn.querySelector("i");
                    if (icon) {
                        icon.classList.remove("fa-sun");
                        icon.classList.add("fa-moon");
                    }
                }
                const mobileDarkModeBtn = document.getElementById("game-toggle-dark-mobile");
                if (mobileDarkModeBtn) {
                    const mobileIcon = mobileDarkModeBtn.querySelector("i");
                    if (mobileIcon) {
                        mobileIcon.classList.remove("fa-sun");
                        mobileIcon.classList.add("fa-moon");
                    }
                }
            }
        }

        // Chess Game Implementation
        document.addEventListener('DOMContentLoaded', function() {
            const chessBoard = document.getElementById('chess-board');
            const fullscreenChessBoard = document.getElementById('fullscreen-chess-board');
            const resetButton = document.getElementById('reset-button');
            const resetScoresButton = document.getElementById('reset-scores-button');
            const undoButton = document.getElementById('undo-button');
            const scoreWhite = document.getElementById('score-white');
            const scoreBlack = document.getElementById('score-black');
            const whiteCaptured = document.getElementById('white-captured');
            const blackCaptured = document.getElementById('black-captured');
            const notification = document.getElementById('notification');
            const promotionModal = document.getElementById('promotion-modal');
            const promotionOptions = document.getElementById('promotion-options');

            const gameModeRadios = document.querySelectorAll('input[name="game-mode"]');
            let gameMode = 'computer';
            const difficultyRadios = document.querySelectorAll('input[name="difficulty"]');
            let difficulty = 'easy';
            
            // Apply active class to initially selected options
            function applyActiveClassToSelectedOptions() {
                document.querySelectorAll('input[name="game-mode"]:checked, input[name="difficulty"]:checked').forEach(radio => {
                    const optionRow = radio.closest('.option-row');
                    if (optionRow) {
                        optionRow.classList.add('active');
                    }
                });
            }
            
            // Apply active class on page load
            applyActiveClassToSelectedOptions();

            let board = [];
            let currentPlayer = 'white';
            let selectedPiece = null;
            let validMoves = [];
            let gameActive = true;
            let scores = { white: 0, black: 0 };
            let capturedPieces = { white: [], black: [] };
            let moveHistory = [];
            let kings = { white: null, black: null };
            let inCheck = { white: false, black: false };
            let promotionPending = null;

            const pieces = {
                'white': { 'king': '♔', 'queen': '♕', 'rook': '♖', 'bishop': '♗', 'knight': '♘', 'pawn': '♙' },
                'black': { 'king': '♚', 'queen': '♛', 'rook': '♜', 'bishop': '♝', 'knight': '♞', 'pawn': '♟' }
            };

            const messages = {
                check: "Check!",
                checkmate: "Checkmate!",
                stalemate: "Stalemate! The game is a draw.",
                whiteTurn: "White's turn",
                blackTurn: "Black's turn",
                whiteWin: "White wins!",
                blackWin: "Black wins!",
                promotion: "Choose a piece for promotion",
                gameStart: "Game started. White moves first."
            };

            // Piece-square tables for Hard level
            const pieceSquareTables = {
                pawn: [
                    [0, 0, 0, 0, 0, 0, 0, 0],
                    [50, 50, 50, 50, 50, 50, 50, 50],
                    [10, 10, 20, 30, 30, 20, 10, 10],
                    [5, 5, 10, 25, 25, 10, 5, 5],
                    [0, 0, 0, 20, 20, 0, 0, 0],
                    [5, -5, -10, 0, 0, -10, -5, 5],
                    [5, 10, 10, -20, -20, 10, 10, 5],
                    [0, 0, 0, 0, 0, 0, 0, 0]
                ],
                knight: [
                    [-50, -40, -30, -30, -30, -30, -40, -50],
                    [-40, -20, 0, 0, 0, 0, -20, -40],
                    [-30, 0, 10, 15, 15, 10, 0, -30],
                    [-30, 5, 15, 20, 20, 15, 5, -30],
                    [-30, 0, 15, 20, 20, 15, 0, -30],
                    [-30, 5, 10, 15, 15, 10, 5, -30],
                    [-40, -20, 0, 5, 5, 0, -20, -40],
                    [-50, -40, -30, -30, -30, -30, -40, -50]
                ]
                // Thêm các bảng khác nếu cần
            };

            // Chess clock variables
            let clockType = 'countdown';
            let timePerMove = 30; // seconds
            let totalTime = 5; // minutes
            let whiteTime = totalTime * 60; // seconds
            let blackTime = totalTime * 60; // seconds
            let currentMoveStartTime = 0;
            let clockInterval = null;
            let activePlayer = 'white';
            
            // Function to update the selected state for control options
            function updateControlOptionSelectedState() {
                // Get all radio inputs in control-options
                const radioInputs = document.querySelectorAll('.control-option input[type="radio"]');
                
                // Update selected state for each
                radioInputs.forEach(input => {
                    const parentLabel = input.closest('.control-option');
                    if (parentLabel) {
                        if (input.checked) {
                            parentLabel.classList.add('selected');
                        } else {
                            parentLabel.classList.remove('selected');
                        }
                    }
                });
            }
            
            function initGame() {
                createBoard();
                setupPieces();
                const { updateFullscreenBoard, updateMainBoard } = initFullscreenMode();
                resetButton.addEventListener('click', resetGame);
                resetScoresButton.addEventListener('click', resetScores);
                undoButton.addEventListener('click', undoMove);
                
                // Initialize chess clock
                initChessClock();
                
                // Initialize selected state for control options
                updateControlOptionSelectedState();

                gameModeRadios.forEach(radio => {
                    radio.addEventListener('change', (e) => {
                        gameMode = e.target.value;
                        const difficultyOptions = document.getElementById('difficulty-options');
                        difficultyOptions.style.display = gameMode === 'computer' ? 'block' : 'none';
                        
                        // Update active class for option-row elements
                        document.querySelectorAll('.option-row').forEach(row => {
                            if (row.querySelector('input[name="game-mode"]')) {
                                row.classList.remove('active');
                            }
                        });
                        const selectedRow = e.target.closest('.option-row');
                        if (selectedRow) {
                            selectedRow.classList.add('active');
                        }
                        
                        // Update player icons in regular mode
                        const blackPlayerIcon = document.querySelector('.black-player .player-icon');
                        const blackClockIcon = document.querySelector('.black-clock .player-icon');
                        
                        if (gameMode === 'computer') {
                            // Update player status icon
                            blackPlayerIcon.className = 'chess-symbol player-icon black-icon';
                            blackPlayerIcon.textContent = '🤖'; // Robot emoji for computer mode
                            
                            // Update clock icon
                            blackClockIcon.className = 'chess-symbol player-icon black-icon';
                            blackClockIcon.textContent = '🤖'; // Robot emoji for computer mode
                        } else {
                            // Update player status icon
                            blackPlayerIcon.className = 'chess-symbol player-icon black-icon';
                            blackPlayerIcon.textContent = '♚'; // Black king for player mode
                            
                            // Update clock icon
                            blackClockIcon.className = 'chess-symbol player-icon black-icon';
                            blackClockIcon.textContent = '♚'; // Black king for player mode
                        }
                        
                        // Update selected state for control options
                        updateControlOptionSelectedState();
                        
                        resetGame();
                    });
                });

                difficultyRadios.forEach(radio => {
                    radio.addEventListener('change', (e) => {
                        difficulty = e.target.value;
                        
                        // Update active class for option-row elements
                        document.querySelectorAll('.option-row').forEach(row => {
                            if (row.querySelector('input[name="difficulty"]')) {
                                row.classList.remove('active');
                            }
                        });
                        const selectedRow = e.target.closest('.option-row');
                        if (selectedRow) {
                            selectedRow.classList.add('active');
                        }
                        
                        // Update selected state for control options
                        updateControlOptionSelectedState();
                        resetGame();
                    });
                });

                const difficultyOptions = document.getElementById('difficulty-options');
                difficultyOptions.style.display = gameMode === 'computer' ? 'block' : 'none';
                
                // Set initial player icons based on game mode
                const blackPlayerIcon = document.querySelector('.black-player .player-icon');
                if (gameMode === 'computer') {
                    blackPlayerIcon.className = 'chess-symbol player-icon black-icon';
                    blackPlayerIcon.textContent = '🤖'; // Robot emoji for computer mode
                } else {
                    blackPlayerIcon.className = 'chess-symbol player-icon black-icon';
                    blackPlayerIcon.textContent = '♚'; // Black king for player mode
                }

                updateScores();
                showNotification(messages.gameStart, 1500);
            }
            
            function initChessClock() {
                const whiteClockDisplay = document.getElementById('white-clock');
                const blackClockDisplay = document.getElementById('black-clock');
                const fullscreenWhiteClockDisplay = document.getElementById('fullscreen-white-clock');
                const fullscreenBlackClockDisplay = document.getElementById('fullscreen-black-clock');
                
                // Set fixed clock settings
                clockType = 'tournament'; // Use tournament clock by default
                timePerMove = 30; // 30 seconds per move
                totalTime = 5; // 5 minutes total
                
                // Initialize clock displays for both normal and fullscreen modes
                updateClockDisplay(whiteClockDisplay, whiteTime);
                updateClockDisplay(blackClockDisplay, blackTime);
                
                if (fullscreenWhiteClockDisplay && fullscreenBlackClockDisplay) {
                    updateClockDisplay(fullscreenWhiteClockDisplay, whiteTime);
                    updateClockDisplay(fullscreenBlackClockDisplay, blackTime);
                }
                
                // Set initial active clock
                updateActiveClockDisplay();
                
                // Start the clock for the first player
                startClock();
            }
            
            function resetClocks() {
                // Stop any running clock
                if (clockInterval) {
                    clearInterval(clockInterval);
                    clockInterval = null;
                }
                
                // Reset times
                whiteTime = totalTime * 60;
                blackTime = totalTime * 60;
                
                // Update displays for both normal and fullscreen modes
                updateClockDisplay(document.getElementById('white-clock'), whiteTime);
                updateClockDisplay(document.getElementById('black-clock'), blackTime);
                
                // Update fullscreen clocks if they exist
                const fullscreenWhiteClock = document.getElementById('fullscreen-white-clock');
                const fullscreenBlackClock = document.getElementById('fullscreen-black-clock');
                if (fullscreenWhiteClock) updateClockDisplay(fullscreenWhiteClock, whiteTime);
                if (fullscreenBlackClock) updateClockDisplay(fullscreenBlackClock, blackTime);
                
                // Reset active player
                activePlayer = 'white';
                updateActiveClockDisplay();
            }
            
            function updateClockDisplay(displayElement, timeInSeconds) {
                const minutes = Math.floor(timeInSeconds / 60);
                const seconds = timeInSeconds % 60;
                displayElement.textContent = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
            }
            
            function updateActiveClockDisplay() {
                // Update normal mode clocks
                const whiteClockContainers = document.querySelectorAll('.white-clock');
                const blackClockContainers = document.querySelectorAll('.black-clock');
                
                if (activePlayer === 'white') {
                    whiteClockContainers.forEach(container => container.classList.add('active-clock'));
                    blackClockContainers.forEach(container => container.classList.remove('active-clock'));
                } else {
                    whiteClockContainers.forEach(container => container.classList.remove('active-clock'));
                    blackClockContainers.forEach(container => container.classList.add('active-clock'));
                }
            }
            
            function startClock() {
                // Stop any existing clock
                if (clockInterval) {
                    clearInterval(clockInterval);
                }
                
                currentMoveStartTime = Date.now();
                
                clockInterval = setInterval(() => {
                    const currentTime = Date.now();
                    const elapsedSeconds = Math.floor((currentTime - currentMoveStartTime) / 1000);
                    
                    if (clockType === 'countdown') {
                        // In countdown mode, we count down from timePerMove for each move
                        const remainingTime = timePerMove - elapsedSeconds;
                        
                        if (remainingTime <= 0) {
                            // Time's up for this move
                            clearInterval(clockInterval);
                            handleTimeUp();
                            return;
                        }
                        
                        // Update the active player's clock display in both normal and fullscreen modes
                        const clockDisplay = document.getElementById(`${activePlayer}-clock`);
                        const fullscreenClockDisplay = document.getElementById(`fullscreen-${activePlayer}-clock`);
                        updateClockDisplay(clockDisplay, remainingTime);
                        if (fullscreenClockDisplay) {
                            updateClockDisplay(fullscreenClockDisplay, remainingTime);
                        }
                    } else {
                        // In tournament mode, we deduct from the player's total time
                        if (activePlayer === 'white') {
                            whiteTime = Math.max(0, totalTime * 60 - elapsedSeconds);
                            updateClockDisplay(document.getElementById('white-clock'), whiteTime);
                            
                            // Update fullscreen clock if it exists
                            const fullscreenWhiteClock = document.getElementById('fullscreen-white-clock');
                            if (fullscreenWhiteClock) {
                                updateClockDisplay(fullscreenWhiteClock, whiteTime);
                            }
                            
                            if (whiteTime <= 0) {
                                clearInterval(clockInterval);
                                handleTimeUp();
                            }
                        } else {
                            blackTime = Math.max(0, totalTime * 60 - elapsedSeconds);
                            updateClockDisplay(document.getElementById('black-clock'), blackTime);
                            
                            // Update fullscreen clock if it exists
                            const fullscreenBlackClock = document.getElementById('fullscreen-black-clock');
                            if (fullscreenBlackClock) {
                                updateClockDisplay(fullscreenBlackClock, blackTime);
                            }
                            
                            if (blackTime <= 0) {
                                clearInterval(clockInterval);
                                handleTimeUp();
                            }
                        }
                    }
                }, 1000);
            }
            
            function switchClock() {
                // Stop the current clock
                if (clockInterval) {
                    clearInterval(clockInterval);
                    clockInterval = null;
                }
                
                // Switch active player
                activePlayer = activePlayer === 'white' ? 'black' : 'white';
                updateActiveClockDisplay();
                
                // Start the clock for the new active player
                startClock();
            }
            
            function handleTimeUp() {
                // Handle time up for the current player
                showNotification(`Time's up for ${activePlayer} player!`, 3000);
                
                // In a real game, this would typically result in a loss for the player
                // For this implementation, we'll just switch to the other player
                switchClock();
            }

            function createBoard() {
                if (!chessBoard) {
                    console.error("Chess board element not found!");
                    return;
                }
                chessBoard.innerHTML = '';
                for (let row = 0; row < 8; row++) {
                    for (let col = 0; col < 8; col++) {
                        const square = document.createElement('div');
                        square.classList.add('chess-square', (row + col) % 2 === 0 ? 'white' : 'black');
                        square.setAttribute('data-row', row);
                        square.setAttribute('data-col', col);

                        // Add rank labels (1-8) to the leftmost column only
                        if (col === 0) {
                            const rankLabel = document.createElement('div');
                            rankLabel.classList.add('rank-label');
                            rankLabel.textContent = 8 - row;
                            square.appendChild(rankLabel);
                        }
                        
                        // Add file labels (a-h) to the bottom row only
                        if (row === 7) {
                            const fileLabel = document.createElement('div');
                            fileLabel.classList.add('file-label');
                            fileLabel.textContent = String.fromCharCode(97 + col);
                            square.appendChild(fileLabel);
                        }

                        square.addEventListener('click', handleSquareClick);
                        chessBoard.appendChild(square);
                    }
                }
            }

            function initFullscreenMode() {
                const fullscreenButton = document.getElementById('fullscreen-button');
                const exitFullscreenButton = document.getElementById('exit-fullscreen-button');
                const fullscreenContainer = document.getElementById('fullscreen-container');
                
                // Get fullscreen game mode and difficulty options
                const fullscreenGameModeOptions = document.querySelectorAll('input[name="fullscreen-game-mode"]');
                const fullscreenDifficultyOptions = document.querySelectorAll('input[name="fullscreen-difficulty"]');
                const fullscreenDifficultyContainer = document.getElementById('fullscreen-difficulty-options');
                
                // Get regular game mode and difficulty options
                const regularGameModeOptions = document.querySelectorAll('input[name="game-mode"]');
                const regularDifficultyOptions = document.querySelectorAll('input[name="difficulty"]');
                
                // Add event listeners for fullscreen control buttons
                const fullscreenResetButton = document.getElementById('fullscreen-reset-button');
                const fullscreenResetScoresButton = document.getElementById('fullscreen-reset-scores-button');
                const fullscreenUndoButton = document.getElementById('fullscreen-undo-button');
                
                if (fullscreenResetButton) {
                    fullscreenResetButton.addEventListener('click', resetGame);
                }
                
                if (fullscreenResetScoresButton) {
                    fullscreenResetScoresButton.addEventListener('click', resetScores);
                }
                
                if (fullscreenUndoButton) {
                    fullscreenUndoButton.addEventListener('click', undoMove);
                }
                
                // Sync game mode between regular and fullscreen
                fullscreenGameModeOptions.forEach(option => {
                    option.addEventListener('change', (e) => {
                        const mode = e.target.value;
                        gameMode = mode;
                        
                        // Update regular mode
                        regularGameModeOptions.forEach(opt => {
                            if (opt.value === mode) opt.checked = true;
                        });
                        
                        // Show/hide difficulty options
                        if (mode === 'computer') {
                            fullscreenDifficultyContainer.style.display = 'block';
                            document.getElementById('difficulty-options').style.display = 'block';
                            
                            // Update player icons in fullscreen mode
                            const fullscreenBlackIcon = document.querySelector('.fullscreen-player-item.player-black .fullscreen-player-icon');
                            if (fullscreenBlackIcon) {
                                fullscreenBlackIcon.innerHTML = '<span class="chess-symbol">🤖</span>'; // Robot emoji
                            }
                            
                            // Update player icons in regular mode
                            const blackPlayerIcon = document.querySelector('.black-player .player-icon');
                            blackPlayerIcon.className = 'chess-symbol player-icon black-icon';
                            blackPlayerIcon.textContent = '🤖'; // Robot emoji
                            
                            // Update clock icon
                            const blackClockIcon = document.querySelector('.black-clock .player-icon');
                            blackClockIcon.className = 'chess-symbol player-icon black-icon';
                            blackClockIcon.textContent = '🤖'; // Robot emoji
                            
                            // Update selected state for control options
                            updateControlOptionSelectedState();
                        } else {
                            fullscreenDifficultyContainer.style.display = 'none';
                            document.getElementById('difficulty-options').style.display = 'none';
                            
                            // Update player icons in fullscreen mode
                            const fullscreenBlackIcon = document.querySelector('.fullscreen-player-item.player-black .fullscreen-player-icon');
                            if (fullscreenBlackIcon) {
                                fullscreenBlackIcon.innerHTML = '<span class="chess-symbol">♚</span>'; // Black king
                            }
                            
                            // Update player icons in regular mode
                            const blackPlayerIcon = document.querySelector('.black-player .player-icon');
                            blackPlayerIcon.className = 'chess-symbol player-icon black-icon';
                            blackPlayerIcon.textContent = '♚'; // Black king
                            
                            // Update clock icon
                            const blackClockIcon = document.querySelector('.black-clock .player-icon');
                            blackClockIcon.className = 'chess-symbol player-icon black-icon';
                            blackClockIcon.textContent = '♚'; // Black king
                            // Update player icons in fullscreen mode
                            if (fullscreenBlackIcon) {
                                fullscreenBlackIcon.textContent = '♚'; // Black king
                            }
                            
                            // Update player icons in regular mode
                            blackPlayerIcon.className = 'chess-symbol player-icon black-icon';
                            blackPlayerIcon.textContent = '♚'; // Black king
                            
                            // Update selected state for control options
                            updateControlOptionSelectedState();
                        }
                        
                        resetGame();
                    });
                });
                
                // Sync difficulty between regular and fullscreen
                fullscreenDifficultyOptions.forEach(option => {
                    option.addEventListener('change', (e) => {
                        const diff = e.target.value;
                        difficulty = diff;
                        
                        // Update regular difficulty
                        regularDifficultyOptions.forEach(opt => {
                            if (opt.value === diff) opt.checked = true;
                        });
                        
                        // Update selected state for control options
                        updateControlOptionSelectedState();
                    });
                });

                fullscreenButton.addEventListener('click', () => {
                    fullscreenContainer.classList.add('active');
                    updateFullscreenBoard();
                    updateScores();
                    updateCapturedPieces(true); // Update captured pieces in fullscreen mode
                    document.body.style.overflow = 'hidden';
                    
                    // Sync current game mode and difficulty to fullscreen
                    fullscreenGameModeOptions.forEach(opt => {
                        if (opt.value === gameMode) opt.checked = true;
                    });
                    
                    fullscreenDifficultyOptions.forEach(opt => {
                        if (opt.value === difficulty) opt.checked = true;
                    });
                    
                    // Show/hide difficulty options based on game mode
                    if (gameMode === 'computer') {
                        fullscreenDifficultyContainer.style.display = 'block';
                        
                        // Update player icons
                        const fullscreenBlackIcon = document.querySelector('.fullscreen-player-item.player-black .fullscreen-player-icon');
                        if (fullscreenBlackIcon) {
                            fullscreenBlackIcon.innerHTML = '<span class="chess-symbol">🤖</span>'; // Robot emoji
                        }
                    } else {
                        fullscreenDifficultyContainer.style.display = 'none';
                        
                        // Update player icons
                        const fullscreenBlackIcon = document.querySelector('.fullscreen-player-item.player-black .fullscreen-player-icon .chess-symbol');
                        if (fullscreenBlackIcon) {
                            fullscreenBlackIcon.textContent = '♚'; // Black king
                        }
                    }
                });

                exitFullscreenButton.addEventListener('click', () => {
                    fullscreenContainer.classList.remove('active');
                    document.body.style.overflow = '';
                    updateMainBoard();
                });

                function createFullscreenBoard() {
                    fullscreenChessBoard.innerHTML = '';
                    for (let row = 0; row < 8; row++) {
                        for (let col = 0; col < 8; col++) {
                            const square = document.createElement('div');
                            square.classList.add('chess-square', (row + col) % 2 === 0 ? 'white' : 'black');
                            square.setAttribute('data-row', row);
                            square.setAttribute('data-col', col);

                            if (col === 0) {
                                const rankLabel = document.createElement('div');
                                rankLabel.classList.add('rank-label');
                                rankLabel.textContent = 8 - row;
                                square.appendChild(rankLabel);
                            }
                            if (row === 7) {
                                const fileLabel = document.createElement('div');
                                fileLabel.classList.add('file-label');
                                fileLabel.textContent = String.fromCharCode(97 + col);
                                square.appendChild(fileLabel);
                            }

                            square.addEventListener('click', handleSquareClick);
                            fullscreenChessBoard.appendChild(square);
                        }
                    }
                }

                function updateFullscreenBoard() {
                    createFullscreenBoard();
                    for (let row = 0; row < 8; row++) {
                        for (let col = 0; col < 8; col++) {
                            const piece = board[row][col];
                            if (piece) {
                                const square = fullscreenChessBoard.querySelector(`[data-row="${row}"][data-col="${col}"]`);
                                addPieceToSquare(square, piece.color, piece.type);
                            }
                        }
                    }
                    updateCheckIndicators(fullscreenChessBoard);
                }

                function updateMainBoard() {
                    for (let row = 0; row < 8; row++) {
                        for (let col = 0; col < 8; col++) {
                            const piece = board[row][col];
                            const square = chessBoard.querySelector(`[data-row="${row}"][data-col="${col}"]`);
                            const existingPiece = square.querySelector('.chess-piece');
                            if (existingPiece) square.removeChild(existingPiece);
                            if (piece) addPieceToSquare(square, piece.color, piece.type);
                        }
                    }
                    updateCheckIndicators(chessBoard);
                }

                createFullscreenBoard();
                return { updateFullscreenBoard, updateMainBoard };
            }

            function setupPieces() {
                board = Array(8).fill().map(() => Array(8).fill(null));
                for (let col = 0; col < 8; col++) {
                    board[1][col] = { type: 'pawn', color: 'black', hasMoved: false };
                    board[6][col] = { type: 'pawn', color: 'white', hasMoved: false };
                }
                const backRowPieces = ['rook', 'knight', 'bishop', 'queen', 'king', 'bishop', 'knight', 'rook'];
                for (let col = 0; col < 8; col++) {
                    board[0][col] = { type: backRowPieces[col], color: 'black', hasMoved: false };
                    board[7][col] = { type: backRowPieces[col], color: 'white', hasMoved: false };
                    if (backRowPieces[col] === 'king') {
                        kings.black = { row: 0, col: col };
                        kings.white = { row: 7, col: col };
                    }
                }
                updateBoardDisplay();
            }

            function updateBoardDisplay() {
                // Clear main board but keep coordinate labels
                const allSquares = chessBoard.querySelectorAll('.chess-square');
                allSquares.forEach(square => {
                    // Remove only chess pieces, keep coordinate labels
                    const chessPieces = square.querySelectorAll('.chess-piece');
                    chessPieces.forEach(piece => {
                        square.removeChild(piece);
                    });
                    
                    // Add coordinate labels if they don't exist
                    const row = parseInt(square.getAttribute('data-row'));
                    const col = parseInt(square.getAttribute('data-col'));
                    
                    // Add rank labels (1-8) to the leftmost column only
                    if (col === 0 && !square.querySelector('.rank-label')) {
                        const rankLabel = document.createElement('div');
                        rankLabel.classList.add('rank-label');
                        rankLabel.textContent = 8 - row;
                        square.appendChild(rankLabel);
                    }
                    
                    // Add file labels (a-h) to the bottom row only
                    if (row === 7 && !square.querySelector('.file-label')) {
                        const fileLabel = document.createElement('div');
                        fileLabel.classList.add('file-label');
                        fileLabel.textContent = String.fromCharCode(97 + col);
                        square.appendChild(fileLabel);
                    }
                });
                
                // Add pieces to main board
                for (let row = 0; row < 8; row++) {
                    for (let col = 0; col < 8; col++) {
                        const piece = board[row][col];
                        if (piece) {
                            const square = chessBoard.querySelector(`[data-row="${row}"][data-col="${col}"]`);
                            addPieceToSquare(square, piece.color, piece.type);
                        }
                    }
                }
                updateCheckIndicators(chessBoard);

                // Update fullscreen board if active
                const fullscreenContainer = document.getElementById('fullscreen-container');
                if (fullscreenContainer.classList.contains('active')) {
                    // Update captured pieces in fullscreen mode
                    updateCapturedPieces(true);
                    
                    const fullscreenSquares = fullscreenChessBoard.querySelectorAll('.chess-square');
                    fullscreenSquares.forEach(square => {
                        // Remove only chess pieces, keep coordinate labels
                        const chessPieces = square.querySelectorAll('.chess-piece');
                        chessPieces.forEach(piece => {
                            square.removeChild(piece);
                        });
                        
                        // Add coordinate labels if they don't exist
                        const row = parseInt(square.getAttribute('data-row'));
                        const col = parseInt(square.getAttribute('data-col'));
                        
                        // Add rank labels (1-8) to the leftmost column only
                        if (col === 0 && !square.querySelector('.rank-label')) {
                            const rankLabel = document.createElement('div');
                            rankLabel.classList.add('rank-label');
                            rankLabel.textContent = 8 - row;
                            square.appendChild(rankLabel);
                        }
                        
                        // Add file labels (a-h) to the bottom row only
                        if (row === 7 && !square.querySelector('.file-label')) {
                            const fileLabel = document.createElement('div');
                            fileLabel.classList.add('file-label');
                            fileLabel.textContent = String.fromCharCode(97 + col);
                            square.appendChild(fileLabel);
                        }
                    });
                    
                    // Add pieces to fullscreen board
                    for (let row = 0; row < 8; row++) {
                        for (let col = 0; col < 8; col++) {
                            const piece = board[row][col];
                            if (piece) {
                                const square = fullscreenChessBoard.querySelector(`[data-row="${row}"][data-col="${col}"]`);
                                addPieceToSquare(square, piece.color, piece.type);
                            }
                        }
                    }
                    updateCheckIndicators(fullscreenChessBoard);
                }
            }

            function addPieceToSquare(square, color, type) {
                const pieceElement = document.createElement('div');
                pieceElement.classList.add('chess-piece', color); // Add color class for styling
                pieceElement.textContent = pieces[color][type];
                pieceElement.setAttribute('data-color', color);
                pieceElement.setAttribute('data-type', type);
                pieceElement.style.zIndex = '5'; // Lower z-index than labels
                
                square.appendChild(pieceElement);
            }

            function handleSquareClick(event) {
                if (!gameActive || (gameMode === 'computer' && currentPlayer === 'black')) return;
                
                // Handle clicks on piece or square
                let target = event.target;
                let square;
                
                if (target.classList.contains('chess-piece')) {
                    // Clicked on the piece
                    square = target.closest('.chess-square');
                } else if (target.classList.contains('chess-square')) {
                    // Clicked directly on the square
                    square = target;
                } else if (target.classList.contains('rank-label') || target.classList.contains('file-label')) {
                    // Clicked on a label, get the parent square
                    square = target.closest('.chess-square');
                } else {
                    return; // Clicked on something else
                }
                
                const row = parseInt(square.getAttribute('data-row'));
                const col = parseInt(square.getAttribute('data-col'));

                if (promotionPending) return;

                if (selectedPiece) {
                    const isValidMove = validMoves.some(move => move.row === row && move.col === col);
                    if (isValidMove) {
                        makeMove(selectedPiece.row, selectedPiece.col, row, col);
                        clearSelection();
                    } else {
                        const piece = board[row][col];
                        if (piece && piece.color === currentPlayer) {
                            clearSelection();
                            selectPiece(row, col);
                        } else {
                            clearSelection();
                        }
                    }
                } else {
                    const piece = board[row][col];
                    if (piece && piece.color === currentPlayer) selectPiece(row, col);
                }
            }

            function selectPiece(row, col) {
                selectedPiece = { row, col };
                const square = getSquareElement(row, col);
                square.classList.add('selected');
                validMoves = getValidMoves(row, col);
                validMoves.forEach(move => {
                    const moveSquare = getSquareElement(move.row, move.col);
                    moveSquare.classList.add('valid-move');
                });
            }

            function clearSelection() {
                if (selectedPiece) {
                    const square = getSquareElement(selectedPiece.row, selectedPiece.col);
                    square.classList.remove('selected');
                }
                document.querySelectorAll('.chess-square').forEach(square => square.classList.remove('valid-move'));
                selectedPiece = null;
                validMoves = [];
            }

            function getValidMoves(row, col) {
                const piece = board[row][col];
                if (!piece) return [];

                let moves = [];
                switch (piece.type) {
                    case 'pawn': moves = getPawnMoves(row, col, piece.color); break;
                    case 'rook': moves = getRookMoves(row, col, piece.color); break;
                    case 'knight': moves = getKnightMoves(row, col, piece.color); break;
                    case 'bishop': moves = getBishopMoves(row, col, piece.color); break;
                    case 'queen': moves = getQueenMoves(row, col, piece.color); break;
                    case 'king': moves = getKingMoves(row, col, piece.color); break;
                }

                return moves.filter(move => {
                    const originalPiece = board[move.row][move.col];
                    const movingPiece = board[row][col];
                    board[move.row][move.col] = movingPiece;
                    board[row][col] = null;

                    let originalKingPos = null;
                    if (movingPiece.type === 'king') {
                        originalKingPos = { ...kings[movingPiece.color] };
                        kings[movingPiece.color] = { row: move.row, col: move.col };
                    }

                    const kingPos = kings[movingPiece.color];
                    const inCheckAfterMove = isSquareAttacked(kingPos.row, kingPos.col, movingPiece.color === 'white' ? 'black' : 'white');

                    board[row][col] = movingPiece;
                    board[move.row][move.col] = originalPiece;
                    if (originalKingPos) kings[movingPiece.color] = originalKingPos;

                    return !inCheckAfterMove;
                });
            }

            function getPawnMoves(row, col, color) {
                const moves = [];
                const direction = color === 'white' ? -1 : 1;
                const startingRow = color === 'white' ? 6 : 1;

                if (isValidPosition(row + direction, col) && !board[row + direction][col]) {
                    moves.push({ row: row + direction, col: col });
                    if (row === startingRow && !board[row + 2 * direction][col]) {
                        moves.push({ row: row + 2 * direction, col: col });
                    }
                }

                const captureCols = [col - 1, col + 1];
                captureCols.forEach(captureCol => {
                    if (isValidPosition(row + direction, captureCol)) {
                        const targetPiece = board[row + direction][captureCol];
                        if (targetPiece && targetPiece.color !== color) {
                            moves.push({ row: row + direction, col: captureCol });
                        }
                    }
                });

                return moves;
            }

            function getRookMoves(row, col, color) { return getStraightMoves(row, col, color); }
            function getKnightMoves(row, col, color) {
                const moves = [];
                const knightMoves = [
                    { row: row - 2, col: col - 1 }, { row: row - 2, col: col + 1 },
                    { row: row - 1, col: col - 2 }, { row: row - 1, col: col + 2 },
                    { row: row + 1, col: col - 2 }, { row: row + 1, col: col + 2 },
                    { row: row + 2, col: col - 1 }, { row: row + 2, col: col + 1 }
                ];
                knightMoves.forEach(move => {
                    if (isValidPosition(move.row, move.col)) {
                        const targetPiece = board[move.row][move.col];
                        if (!targetPiece || targetPiece.color !== color) moves.push(move);
                    }
                });
                return moves;
            }
            function getBishopMoves(row, col, color) { return getDiagonalMoves(row, col, color); }
            function getQueenMoves(row, col, color) { return [...getStraightMoves(row, col, color), ...getDiagonalMoves(row, col, color)]; }
            function getKingMoves(row, col, color) {
                const moves = [];
                const directions = [
                    { row: -1, col: -1 }, { row: -1, col: 0 }, { row: -1, col: 1 },
                    { row: 0, col: -1 }, { row: 0, col: 1 },
                    { row: 1, col: -1 }, { row: 1, col: 0 }, { row: 1, col: 1 }
                ];
                directions.forEach(dir => {
                    const newRow = row + dir.row;
                    const newCol = col + dir.col;
                    if (isValidPosition(newRow, newCol)) {
                        const targetPiece = board[newRow][newCol];
                        if (!targetPiece || targetPiece.color !== color) {
                            if (!isSquareAttacked(newRow, newCol, color === 'white' ? 'black' : 'white')) {
                                moves.push({ row: newRow, col: newCol });
                            }
                        }
                    }
                });

                if (!board[row][col].hasMoved && !inCheck[color]) {
                    if (board[row][7] && board[row][7].type === 'rook' && !board[row][7].hasMoved &&
                        !board[row][5] && !board[row][6] &&
                        !isSquareAttacked(row, 5, color === 'white' ? 'black' : 'white') &&
                        !isSquareAttacked(row, 6, color === 'white' ? 'black' : 'white')) {
                        moves.push({ row: row, col: 6, castling: 'kingside' });
                    }
                    if (board[row][0] && board[row][0].type === 'rook' && !board[row][0].hasMoved &&
                        !board[row][1] && !board[row][2] && !board[row][3] &&
                        !isSquareAttacked(row, 2, color === 'white' ? 'black' : 'white') &&
                        !isSquareAttacked(row, 3, color === 'white' ? 'black' : 'white')) {
                        moves.push({ row: row, col: 2, castling: 'queenside' });
                    }
                }
                return moves;
            }

            function getStraightMoves(row, col, color) {
                const moves = [];
                const directions = [{ row: -1, col: 0 }, { row: 1, col: 0 }, { row: 0, col: -1 }, { row: 0, col: 1 }];
                directions.forEach(dir => {
                    let newRow = row + dir.row;
                    let newCol = col + dir.col;
                    while (isValidPosition(newRow, newCol)) {
                        const targetPiece = board[newRow][newCol];
                        if (!targetPiece) moves.push({ row: newRow, col: newCol });
                        else {
                            if (targetPiece.color !== color) moves.push({ row: newRow, col: newCol });
                            break;
                        }
                        newRow += dir.row;
                        newCol += dir.col;
                    }
                });
                return moves;
            }

            function getDiagonalMoves(row, col, color) {
                const moves = [];
                const directions = [{ row: -1, col: -1 }, { row: -1, col: 1 }, { row: 1, col: -1 }, { row: 1, col: 1 }];
                directions.forEach(dir => {
                    let newRow = row + dir.row;
                    let newCol = col + dir.col;
                    while (isValidPosition(newRow, newCol)) {
                        const targetPiece = board[newRow][newCol];
                        if (!targetPiece) moves.push({ row: newRow, col: newCol });
                        else {
                            if (targetPiece.color !== color) moves.push({ row: newRow, col: newCol });
                            break;
                        }
                        newRow += dir.row;
                        newCol += dir.col;
                    }
                });
                return moves;
            }

            function isValidPosition(row, col) { return row >= 0 && row < 8 && col >= 0 && col < 8; }

            function isSquareAttacked(row, col, attackerColor) {
                const pawnDirection = attackerColor === 'white' ? -1 : 1;
                const pawnAttackCols = [col - 1, col + 1];
                for (const attackCol of pawnAttackCols) {
                    const attackRow = row + pawnDirection;
                    if (isValidPosition(attackRow, attackCol) && board[attackRow][attackCol]?.type === 'pawn' && board[attackRow][attackCol]?.color === attackerColor) return true;
                }

                const knightMoves = [
                    { row: row - 2, col: col - 1 }, { row: row - 2, col: col + 1 },
                    { row: row - 1, col: col - 2 }, { row: row - 1, col: col + 2 },
                    { row: row + 1, col: col - 2 }, { row: row + 1, col: col + 2 },
                    { row: row + 2, col: col - 1 }, { row: row + 2, col: col + 1 }
                ];
                for (const move of knightMoves) {
                    if (isValidPosition(move.row, move.col) && board[move.row][move.col]?.type === 'knight' && board[move.row][move.col]?.color === attackerColor) return true;
                }

                const straightDirections = [{ row: -1, col: 0 }, { row: 1, col: 0 }, { row: 0, col: -1 }, { row: 0, col: 1 }];
                for (const dir of straightDirections) {
                    let newRow = row + dir.row;
                    let newCol = col + dir.col;
                    while (isValidPosition(newRow, newCol)) {
                        const piece = board[newRow][newCol];
                        if (piece) {
                            if (piece.color === attackerColor && (piece.type === 'rook' || piece.type === 'queen')) return true;
                            break;
                        }
                        newRow += dir.row;
                        newCol += dir.col;
                    }
                }

                const diagonalDirections = [{ row: -1, col: -1 }, { row: -1, col: 1 }, { row: 1, col: -1 }, { row: 1, col: 1 }];
                for (const dir of diagonalDirections) {
                    let newRow = row + dir.row;
                    let newCol = col + dir.col;
                    while (isValidPosition(newRow, newCol)) {
                        const piece = board[newRow][newCol];
                        if (piece) {
                            if (piece.color === attackerColor && (piece.type === 'bishop' || piece.type === 'queen')) return true;
                            break;
                        }
                        newRow += dir.row;
                        newCol += dir.col;
                    }
                }

                const kingMoves = [
                    { row: row - 1, col: col - 1 }, { row: row - 1, col: col }, { row: row - 1, col: col + 1 },
                    { row: row, col: col - 1 }, { row: row, col: col + 1 },
                    { row: row + 1, col: col - 1 }, { row: row + 1, col: col }, { row: row + 1, col: col + 1 }
                ];
                for (const move of kingMoves) {
                    if (isValidPosition(move.row, move.col) && board[move.row][move.col]?.type === 'king' && board[move.row][move.col]?.color === attackerColor) return true;
                }
                return false;
            }

            function makeMove(fromRow, fromCol, toRow, toCol) {
                const piece = board[fromRow][fromCol];
                const targetPiece = board[toRow][toCol];
                moveHistory.push({
                    from: { row: fromRow, col: fromCol },
                    to: { row: toRow, col: toCol },
                    piece: { ...piece },
                    captured: targetPiece ? { ...targetPiece } : null,
                    kings: { ...kings },
                    inCheck: { ...inCheck }
                });

                if (piece.type === 'king' && Math.abs(fromCol - toCol) === 2) {
                    if (toCol === 6) {
                        board[fromRow][5] = board[fromRow][7];
                        board[fromRow][7] = null;
                        board[fromRow][5].hasMoved = true;
                    } else if (toCol === 2) {
                        board[fromRow][3] = board[fromRow][0];
                        board[fromRow][0] = null;
                        board[fromRow][3].hasMoved = true;
                    }
                }

                if (targetPiece) {
                    capturedPieces[piece.color].push(targetPiece);
                    // Check if in fullscreen mode
                    const fullscreenContainer = document.getElementById('fullscreen-container');
                    const isFullscreen = fullscreenContainer && fullscreenContainer.classList.contains('active');
                    updateCapturedPieces(isFullscreen);
                    if (targetPiece.type === 'king') {
                        gameActive = false;
                        if (targetPiece.color === 'white') {
                            scores.black++;
                            showNotification(messages.blackWin + " King captured!", 3000);
                        } else {
                            scores.white++;
                            showNotification(messages.whiteWin + " King captured!", 3000);
                        }
                        updateScores();
                        board[toRow][toCol] = piece;
                        board[fromRow][fromCol] = null;
                        updateBoardDisplay();
                        return;
                    }
                }

                board[toRow][toCol] = piece;
                board[fromRow][fromCol] = null;
                board[toRow][toCol].hasMoved = true;

                if (piece.type === 'king') kings[piece.color] = { row: toRow, col: toCol };

                if (piece.type === 'pawn' && (toRow === 0 || toRow === 7)) {
                    promotionPending = { row: toRow, col: toCol, color: piece.color };
                    showPromotionOptions(piece.color);
                    return;
                }

                completeMove();
            }

            function completeMove() {
                currentPlayer = currentPlayer === 'white' ? 'black' : 'white';
                
                // Check if in fullscreen mode
                const fullscreenContainer = document.getElementById('fullscreen-container');
                const isFullscreen = fullscreenContainer && fullscreenContainer.classList.contains('active');
                
                // Update board display will also update captured pieces in fullscreen mode
                updateBoardDisplay();
                
                // Make sure captured pieces are updated in fullscreen mode
                if (isFullscreen) {
                    updateCapturedPieces(true);
                }
                
                // Switch the chess clock
                activePlayer = currentPlayer;
                updateActiveClockDisplay();
                startClock();
                
                checkGameStatus();
                if (gameActive && gameMode === 'computer' && currentPlayer === 'black') {
                    setTimeout(makeComputerMove, 500);
                }
            }

            function showPromotionOptions(color) {
                promotionModal.classList.add('active');
                promotionOptions.innerHTML = '';
                const pieceTypes = ['queen', 'rook', 'bishop', 'knight'];
                pieceTypes.forEach(type => {
                    const pieceElement = document.createElement('div');
                    pieceElement.classList.add('promotion-piece', 'chess-piece', color);
                    pieceElement.textContent = pieces[color][type];
                    pieceElement.setAttribute('data-type', type);
                    pieceElement.addEventListener('click', () => promotePawn(type));
                    
                    // Create a wrapper for better centering
                    const pieceWrapper = document.createElement('span');
                    pieceWrapper.style.display = 'flex';
                    pieceWrapper.style.justifyContent = 'center';
                    pieceWrapper.style.alignItems = 'center';
                    pieceWrapper.appendChild(pieceElement);
                    
                    promotionOptions.appendChild(pieceWrapper);
                });
            }

            function promotePawn(pieceType) {
                if (!promotionPending) return;
                board[promotionPending.row][promotionPending.col].type = pieceType;
                promotionModal.classList.remove('active');
                promotionPending = null;
                completeMove();
            }

            function checkGameStatus() {
                inCheck.white = isSquareAttacked(kings.white.row, kings.white.col, 'black');
                inCheck.black = isSquareAttacked(kings.black.row, kings.black.col, 'white');
                updateCheckIndicators(chessBoard);

                const hasValidMoves = checkForValidMoves(currentPlayer);
                if (inCheck[currentPlayer]) {
                    if (!hasValidMoves) {
                        gameActive = false;
                        if (currentPlayer === 'white') {
                            scores.black++;
                            showNotification(messages.blackWin, 2000);
                        } else {
                            scores.white++;
                            showNotification(messages.whiteWin, 2000);
                        }
                        updateScores();
                    } else {
                        showNotification(messages.check, 1500);
                    }
                } else if (!hasValidMoves) {
                    gameActive = false;
                    showNotification(messages.stalemate, 2000);
                }
            }

            function checkForValidMoves(player) {
                for (let row = 0; row < 8; row++) {
                    for (let col = 0; col < 8; col++) {
                        const piece = board[row][col];
                        if (piece && piece.color === player) {
                            if (getValidMoves(row, col).length > 0) return true;
                        }
                    }
                }
                return false;
            }

            function updateCheckIndicators(boardElement) {
                const allSquares = boardElement.querySelectorAll('.chess-square');
                allSquares.forEach(square => square.classList.remove('check'));
                if (inCheck.white) {
                    const kingSquare = boardElement.querySelector(`[data-row="${kings.white.row}"][data-col="${kings.white.col}"]`);
                    if (kingSquare) kingSquare.classList.add('check');
                }
                if (inCheck.black) {
                    const kingSquare = boardElement.querySelector(`[data-row="${kings.black.row}"][data-col="${kings.black.col}"]`);
                    if (kingSquare) kingSquare.classList.add('check');
                }
            }

            function makeComputerMove() {
                if (!gameActive || currentPlayer !== 'black') return;

                let bestMove = null;
                let bestScore = -Infinity;
                const possibleMoves = [];

                for (let row = 0; row < 8; row++) {
                    for (let col = 0; col < 8; col++) {
                        const piece = board[row][col];
                        if (piece && piece.color === 'black') {
                            const moves = getValidMoves(row, col);
                            moves.forEach(move => {
                                possibleMoves.push({ fromRow: row, fromCol: col, toRow: move.row, toCol: move.col });
                            });
                        }
                    }
                }

                if (difficulty === 'easy') {
                    const captureMoves = possibleMoves.filter(move => board[move.toRow][move.toCol] !== null);
                    if (captureMoves.length > 0) {
                        const randomIndex = Math.floor(Math.random() * captureMoves.length);
                        bestMove = captureMoves[randomIndex];
                    } else {
                        const randomIndex = Math.floor(Math.random() * possibleMoves.length);
                        bestMove = possibleMoves[randomIndex];
                    }
                    bestScore = 0;
                } else if (difficulty === 'medium') {
                    possibleMoves.forEach(move => {
                        const piece = board[move.fromRow][move.fromCol];
                        const targetPiece = board[move.toRow][move.toCol];
                        board[move.toRow][move.toCol] = piece;
                        board[move.fromRow][move.fromCol] = null;

                        let originalKingPos = null;
                        if (piece.type === 'king') {
                            originalKingPos = { ...kings.black };
                            kings.black = { row: move.toRow, col: move.toCol };
                        }

                        const score = minimax(2, false);

                        board[move.fromRow][move.fromCol] = piece;
                        board[move.toRow][move.toCol] = targetPiece;
                        if (originalKingPos) kings.black = originalKingPos;

                        if (score > bestScore) {
                            bestScore = score;
                            bestMove = move;
                        }
                    });
                } else { // Hard
                    possibleMoves.forEach(move => {
                        const piece = board[move.fromRow][move.fromCol];
                        const targetPiece = board[move.toRow][move.toCol];
                        board[move.toRow][move.toCol] = piece;
                        board[move.fromRow][move.fromCol] = null;

                        let originalKingPos = null;
                        if (piece.type === 'king') {
                            originalKingPos = { ...kings.black };
                            kings.black = { row: move.toRow, col: move.toCol };
                        }

                        const score = minimaxAlphaBeta(4, -Infinity, Infinity, false);

                        board[move.fromRow][move.fromCol] = piece;
                        board[move.toRow][move.toCol] = targetPiece;
                        if (originalKingPos) kings.black = originalKingPos;

                        if (score > bestScore) {
                            bestScore = score;
                            bestMove = move;
                        }
                    });
                }

                if (bestMove) makeMove(bestMove.fromRow, bestMove.fromCol, bestMove.toRow, bestMove.toCol);
            }

            function minimax(depth, isMaximizing) {
                if (depth === 0) return evaluateBoardMedium();

                if (isMaximizing) {
                    let maxEval = -Infinity;
                    const moves = getAllPossibleMoves('black');
                    for (const move of moves) {
                        const piece = board[move.fromRow][move.fromCol];
                        const targetPiece = board[move.toRow][move.toCol];
                        board[move.toRow][move.toCol] = piece;
                        board[move.fromRow][move.fromCol] = null;

                        let originalKingPos = null;
                        if (piece.type === 'king') {
                            originalKingPos = { ...kings.black };
                            kings.black = { row: move.toRow, col: move.toCol };
                        }

                        const evalScore = minimax(depth - 1, false);
                        maxEval = Math.max(maxEval, evalScore);

                        board[move.fromRow][move.fromCol] = piece;
                        board[move.toRow][move.toCol] = targetPiece;
                        if (originalKingPos) kings.black = originalKingPos;
                    }
                    return maxEval;
                } else {
                    let minEval = Infinity;
                    const moves = getAllPossibleMoves('white');
                    for (const move of moves) {
                        const piece = board[move.fromRow][move.fromCol];
                        const targetPiece = board[move.toRow][move.toCol];
                        board[move.toRow][move.toCol] = piece;
                        board[move.fromRow][move.fromCol] = null;

                        let originalKingPos = null;
                        if (piece.type === 'king') {
                            originalKingPos = { ...kings.white };
                            kings.white = { row: move.toRow, col: move.toCol };
                        }

                        const evalScore = minimax(depth - 1, true);
                        minEval = Math.min(minEval, evalScore);

                        board[move.fromRow][move.fromCol] = piece;
                        board[move.toRow][move.toCol] = targetPiece;
                        if (originalKingPos) kings.white = originalKingPos;
                    }
                    return minEval;
                }
            }

            function minimaxAlphaBeta(depth, alpha, beta, isMaximizing) {
                if (depth === 0) return evaluateBoardHard();

                if (isMaximizing) {
                    let maxEval = -Infinity;
                    const moves = getAllPossibleMoves('black');
                    for (const move of moves) {
                        const piece = board[move.fromRow][move.fromCol];
                        const targetPiece = board[move.toRow][move.toCol];
                        board[move.toRow][move.toCol] = piece;
                        board[move.fromRow][move.fromCol] = null;

                        let originalKingPos = null;
                        if (piece.type === 'king') {
                            originalKingPos = { ...kings.black };
                            kings.black = { row: move.toRow, col: move.toCol };
                        }

                        const evalScore = minimaxAlphaBeta(depth - 1, alpha, beta, false);
                        maxEval = Math.max(maxEval, evalScore);
                        alpha = Math.max(alpha, evalScore);

                        board[move.fromRow][move.fromCol] = piece;
                        board[move.toRow][move.toCol] = targetPiece;
                        if (originalKingPos) kings.black = originalKingPos;

                        if (beta <= alpha) break;
                    }
                    return maxEval;
                } else {
                    let minEval = Infinity;
                    const moves = getAllPossibleMoves('white');
                    for (const move of moves) {
                        const piece = board[move.fromRow][move.fromCol];
                        const targetPiece = board[move.toRow][move.toCol];
                        board[move.toRow][move.toCol] = piece;
                        board[move.fromRow][move.fromCol] = null;

                        let originalKingPos = null;
                        if (piece.type === 'king') {
                            originalKingPos = { ...kings.white };
                            kings.white = { row: move.toRow, col: move.toCol };
                        }

                        const evalScore = minimaxAlphaBeta(depth - 1, alpha, beta, true);
                        minEval = Math.min(minEval, evalScore);
                        beta = Math.min(beta, evalScore);

                        board[move.fromRow][move.fromCol] = piece;
                        board[move.toRow][move.toCol] = targetPiece;
                        if (originalKingPos) kings.white = originalKingPos;

                        if (beta <= alpha) break;
                    }
                    return minEval;
                }
            }

            function getAllPossibleMoves(player) {
                const moves = [];
                for (let row = 0; row < 8; row++) {
                    for (let col = 0; col < 8; col++) {
                        const piece = board[row][col];
                        if (piece && piece.color === player) {
                            const validMoves = getValidMoves(row, col);
                            validMoves.forEach(move => {
                                moves.push({ fromRow: row, fromCol: col, toRow: move.row, toCol: move.col });
                            });
                        }
                    }
                }
                return moves;
            }

            function evaluateBoardMedium() {
                let score = 0;

                for (let row = 0; row < 8; row++) {
                    for (let col = 0; col < 8; col++) {
                        const piece = board[row][col];
                        if (piece) {
                            const pieceValue = getPieceValue(piece.type);
                            score += piece.color === 'black' ? pieceValue : -pieceValue;
                        }
                    }
                }

                const undevelopedPenalty = -0.5;
                const backRow = currentPlayer === 'white' ? 7 : 0;
                for (let col = 0; col < 8; col++) {
                    const piece = board[backRow][col];
                    if (piece && piece.color === currentPlayer && !piece.hasMoved) score += undevelopedPenalty;
                }

                const centerSquares = [{ row: 3, col: 3 }, { row: 3, col: 4 }, { row: 4, col: 3 }, { row: 4, col: 4 }];
                const centerControlBonus = 0.1;
                centerSquares.forEach(square => {
                    const piece = board[square.row][square.col];
                    if (piece && piece.color === currentPlayer) score += centerControlBonus;
                    else if (piece) score -= centerControlBonus;
                });

                return score;
            }

            function evaluateBoardHard() {
                let score = 0;

                for (let row = 0; row < 8; row++) {
                    for (let col = 0; col < 8; col++) {
                        const piece = board[row][col];
                        if (piece) {
                            const pieceValue = getPieceValue(piece.type);
                            let tableValue = 0;
                            if (pieceSquareTables[piece.type]) {
                                tableValue = piece.color === 'black' ? pieceSquareTables[piece.type][row][col] : pieceSquareTables[piece.type][7 - row][col];
                            }
                            score += piece.color === 'black' ? (pieceValue + tableValue) : -(pieceValue + tableValue);
                        }
                    }
                }

                if (isSquareAttacked(kings.white.row, kings.white.col, 'black')) score += 50;
                if (isSquareAttacked(kings.black.row, kings.black.col, 'white')) score -= 50;

                return score;
            }

            function getPieceValue(pieceType) {
                switch (pieceType) {
                    case 'pawn': return 1;
                    case 'knight': return 3;
                    case 'bishop': return 3;
                    case 'rook': return 5;
                    case 'queen': return 9;
                    case 'king': return 100;
                    default: return 0;
                }
            }

            function undoMove() {
                if (moveHistory.length === 0) return;
                const lastMove = moveHistory.pop();
                board[lastMove.from.row][lastMove.from.col] = lastMove.piece;
                board[lastMove.to.row][lastMove.to.col] = lastMove.captured;
                kings = { ...lastMove.kings };
                inCheck = { ...lastMove.inCheck };

                if (lastMove.piece.type === 'king' && Math.abs(lastMove.from.col - lastMove.to.col) === 2) {
                    if (lastMove.to.col === 6) {
                        board[lastMove.from.row][7] = board[lastMove.from.row][5];
                        board[lastMove.from.row][5] = null;
                        board[lastMove.from.row][7].hasMoved = false;
                    } else if (lastMove.to.col === 2) {
                        board[lastMove.from.row][0] = board[lastMove.from.row][3];
                        board[lastMove.from.row][3] = null;
                        board[lastMove.from.row][0].hasMoved = false;
                    }
                }

                if (lastMove.captured) {
                    capturedPieces[lastMove.piece.color].pop();
                    // Check if in fullscreen mode
                    const fullscreenContainer = document.getElementById('fullscreen-container');
                    const isFullscreen = fullscreenContainer && fullscreenContainer.classList.contains('active');
                    updateCapturedPieces(isFullscreen);
                }

                currentPlayer = currentPlayer === 'white' ? 'black' : 'white';
                updateBoardDisplay();
                gameActive = true;
            }

            function updateCapturedPieces(isFullscreen = false) {
                whiteCaptured.innerHTML = '';
                blackCaptured.innerHTML = '';
                
                // Get fullscreen captured pieces containers
                const fullscreenWhiteCaptured = document.getElementById('fullscreen-white-captured');
                const fullscreenBlackCaptured = document.getElementById('fullscreen-black-captured');
                
                // Clear fullscreen captured pieces if in fullscreen mode
                if (isFullscreen && fullscreenWhiteCaptured && fullscreenBlackCaptured) {
                    fullscreenWhiteCaptured.innerHTML = '';
                    fullscreenBlackCaptured.innerHTML = '';
                }
                
                // Define piece order (from lowest to highest value)
                const pieceOrder = ['pawn', 'knight', 'bishop', 'rook', 'queen', 'king'];
                
                // Count pieces by type for white captured pieces (black pieces that were captured)
                const whiteCapturedCount = {};
                capturedPieces.white.forEach(piece => {
                    if (!whiteCapturedCount[piece.type]) {
                        whiteCapturedCount[piece.type] = 0;
                    }
                    whiteCapturedCount[piece.type]++;
                });
                
                // Display white captured pieces with counts in specific order
                pieceOrder.forEach(pieceType => {
                    if (whiteCapturedCount[pieceType]) {
                        // For regular display
                        const pieceElement = document.createElement('div');
                        pieceElement.classList.add('captured-piece', 'chess-piece', 'black');
                        pieceElement.textContent = pieces.black[pieceType];
                        
                        // Create a wrapper for better display
                        const pieceWrapper = document.createElement('span');
                        pieceWrapper.style.display = 'flex';
                        pieceWrapper.style.flexDirection = 'row';
                        pieceWrapper.style.alignItems = 'center';
                        pieceWrapper.style.margin = '0 5px';
                        
                        // Add the piece
                        pieceWrapper.appendChild(pieceElement);
                        
                        // Add the count if more than 1
                        if (whiteCapturedCount[pieceType] > 1) {
                            const countElement = document.createElement('div');
                            countElement.style.fontSize = '12px';
                            countElement.style.fontWeight = 'bold';
                            countElement.style.marginLeft = '2px';
                            countElement.textContent = 'x' + whiteCapturedCount[pieceType];
                            pieceWrapper.appendChild(countElement);
                        }
                        
                        whiteCaptured.appendChild(pieceWrapper);
                        
                        // For fullscreen display
                        if (isFullscreen && fullscreenWhiteCaptured) {
                            const fsElement = document.createElement('div');
                            fsElement.classList.add('captured-piece');
                            fsElement.textContent = pieces.black[pieceType];
                            
                            const fsWrapper = document.createElement('span');
                            fsWrapper.style.display = 'flex';
                            fsWrapper.style.flexDirection = 'row';
                            fsWrapper.style.alignItems = 'center';
                            fsWrapper.style.margin = '0 5px';
                            
                            fsWrapper.appendChild(fsElement);
                            
                            if (whiteCapturedCount[pieceType] > 1) {
                                const fsCountElement = document.createElement('div');
                                fsCountElement.style.fontSize = '12px';
                                fsCountElement.style.fontWeight = 'bold';
                                fsCountElement.style.marginLeft = '2px';
                                fsCountElement.style.color = 'white';
                                fsCountElement.textContent = 'x' + whiteCapturedCount[pieceType];
                                fsWrapper.appendChild(fsCountElement);
                            }
                            
                            fullscreenWhiteCaptured.appendChild(fsWrapper);
                        }
                    }
                });
                
                // Count pieces by type for black captured pieces (white pieces that were captured)
                const blackCapturedCount = {};
                capturedPieces.black.forEach(piece => {
                    if (!blackCapturedCount[piece.type]) {
                        blackCapturedCount[piece.type] = 0;
                    }
                    blackCapturedCount[piece.type]++;
                });
                
                // Display black captured pieces with counts in specific order
                pieceOrder.forEach(pieceType => {
                    if (blackCapturedCount[pieceType]) {
                        // For regular display
                        const pieceElement = document.createElement('div');
                        pieceElement.classList.add('captured-piece', 'chess-piece', 'white');
                        pieceElement.textContent = pieces.white[pieceType];
                        
                        // Create a wrapper for better display
                        const pieceWrapper = document.createElement('span');
                        pieceWrapper.style.display = 'flex';
                        pieceWrapper.style.flexDirection = 'row';
                        pieceWrapper.style.alignItems = 'center';
                        pieceWrapper.style.margin = '0 5px';
                        
                        // Add the piece
                        pieceWrapper.appendChild(pieceElement);
                        
                        // Add the count if more than 1
                        if (blackCapturedCount[pieceType] > 1) {
                            const countElement = document.createElement('div');
                            countElement.style.fontSize = '12px';
                            countElement.style.fontWeight = 'bold';
                            countElement.style.marginLeft = '2px';
                            countElement.textContent = 'x' + blackCapturedCount[pieceType];
                            pieceWrapper.appendChild(countElement);
                        }
                        
                        blackCaptured.appendChild(pieceWrapper);
                        
                        // For fullscreen display
                        if (isFullscreen && fullscreenBlackCaptured) {
                            const fsElement = document.createElement('div');
                            fsElement.classList.add('captured-piece');
                            fsElement.textContent = pieces.white[pieceType];
                            
                            const fsWrapper = document.createElement('span');
                            fsWrapper.style.display = 'flex';
                            fsWrapper.style.flexDirection = 'row';
                            fsWrapper.style.alignItems = 'center';
                            fsWrapper.style.margin = '0 5px';
                            
                            fsWrapper.appendChild(fsElement);
                            
                            if (blackCapturedCount[pieceType] > 1) {
                                const fsCountElement = document.createElement('div');
                                fsCountElement.style.fontSize = '12px';
                                fsCountElement.style.fontWeight = 'bold';
                                fsCountElement.style.marginLeft = '2px';
                                fsCountElement.style.color = 'white';
                                fsCountElement.textContent = 'x' + blackCapturedCount[pieceType];
                                fsWrapper.appendChild(fsCountElement);
                            }
                            
                            fullscreenBlackCaptured.appendChild(fsWrapper);
                        }
                    }
                });
            }

            function updateScores() {
                scoreWhite.textContent = scores.white;
                scoreBlack.textContent = scores.black;
                const fullscreenScoreWhite = document.getElementById('fullscreen-score-white');
                const fullscreenScoreBlack = document.getElementById('fullscreen-score-black');
                if (fullscreenScoreWhite) fullscreenScoreWhite.textContent = scores.white;
                if (fullscreenScoreBlack) fullscreenScoreBlack.textContent = scores.black;
            }

            function resetGame() {
                currentPlayer = 'white';
                gameActive = true;
                selectedPiece = null;
                validMoves = [];
                moveHistory = [];
                capturedPieces = { white: [], black: [] };
                inCheck = { white: false, black: false };
                promotionPending = null;
                promotionModal.classList.remove('active');
                
                // Reset chess clocks
                resetClocks();
                
                // Start the clock for white player
                activePlayer = 'white';
                updateActiveClockDisplay();
                startClock();
                
                setupPieces();
                
                // Check if in fullscreen mode
                const fullscreenContainer = document.getElementById('fullscreen-container');
                const isFullscreen = fullscreenContainer && fullscreenContainer.classList.contains('active');
                updateCapturedPieces(isFullscreen);
                
                showNotification(messages.gameStart, 1500);
                if (fullscreenContainer.classList.contains('active')) {
                    const { updateFullscreenBoard } = initFullscreenMode();
                    updateFullscreenBoard();
                }
            }

            function resetScores() {
                scores.white = 0;
                scores.black = 0;
                updateScores();
            }

            function showNotification(message, duration = 2000) {
                if (window.notificationTimeout) clearTimeout(window.notificationTimeout);
                notification.classList.remove('show');
                notification.textContent = message;
                setTimeout(() => {
                    notification.classList.add('show');
                    window.notificationTimeout = setTimeout(() => notification.classList.remove('show'), duration);
                }, 50);
            }

            function getSquareElement(row, col) {
                const fullscreenContainer = document.getElementById('fullscreen-container');
                const boardElement = fullscreenContainer.classList.contains('active') ? fullscreenChessBoard : chessBoard;
                return boardElement.querySelector(`[data-row="${row}"][data-col="${col}"]`);
            }

            initGame();

            const dropdownSubmenus = document.querySelectorAll('.dropdown-submenu');
            dropdownSubmenus.forEach(submenu => {
                const submenuHeader = submenu.querySelector('.submenu-header');
                if (submenuHeader) {
                    submenuHeader.addEventListener('click', function(e) {
                        e.preventDefault();
                        e.stopPropagation();
                        if (window.innerWidth <= 767) {
                            if (navLinks) navLinks.classList.add('show');
                            if (mobileMenuToggle) {
                                mobileMenuToggle.classList.add('active');
                                const barsIcon = mobileMenuToggle.querySelector('.menu-icon-bars');
                                const timesIcon = mobileMenuToggle.querySelector('.menu-icon-times');
                                if (barsIcon && timesIcon) {
                                    barsIcon.style.display = 'none';
                                    timesIcon.style.display = 'inline-block';
                                }
                            }
                            const parentDropdown = submenu.closest('.dropdown');
                            if (parentDropdown && !parentDropdown.classList.contains('active')) parentDropdown.classList.add('active');
                        }
                        dropdownSubmenus.forEach(otherSubmenu => {
                            if (otherSubmenu !== submenu && otherSubmenu.classList.contains('active')) otherSubmenu.classList.remove('active');
                        });
                        submenu.classList.toggle('active');
                    });
                }
            });

            const submenuLinks = document.querySelectorAll('.dropdown-submenu .dropdown-content a');
            submenuLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    if (window.innerWidth <= 767) {
                        e.stopPropagation();
                        if (navLinks) navLinks.classList.add('show');
                        if (mobileMenuToggle) {
                            mobileMenuToggle.classList.add('active');
                            const barsIcon = mobileMenuToggle.querySelector('.menu-icon-bars');
                            const timesIcon = mobileMenuToggle.querySelector('.menu-icon-times');
                            if (barsIcon && timesIcon) {
                                barsIcon.style.display = 'none';
                                timesIcon.style.display = 'inline-block';
                            }
                        }
                        const href = this.getAttribute('href');
                        if (href && href !== 'javascript:void(0)') {
                            e.preventDefault();
                            setTimeout(() => window.location.href = href, 50);
                        }
                    }
                });
            });

            document.addEventListener('click', function(e) {
                if (!e.target.closest('.dropdown-submenu') && !e.target.closest('.mobile-menu-toggle') && !e.target.closest('.nav-links')) {
                    dropdownSubmenus.forEach(submenu => submenu.classList.remove('active'));
                }
            });
        });
    </script>
</body>
</html>